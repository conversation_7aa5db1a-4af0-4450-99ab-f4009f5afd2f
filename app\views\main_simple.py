# -*- coding: utf-8 -*-
"""
简化版主页面视图
"""

from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from app.utils.decorators import permission_required, menu_permission_required
from app.forms.content import GenerateContentForm
from app.models.client import Client
from app.models.template import Template, TemplateCategory
from app.models.task import Task, Batch
from app.models.content import Content
from app import db, csrf
from datetime import datetime
import random
import re
import json

# 创建简化版主页面蓝图
main_simple_bp = Blueprint('main_simple', __name__, url_prefix='/simple')


@main_simple_bp.route('/')
@login_required
def index():
    """简化版主页面"""
    return render_template('main_simple.html')


@main_simple_bp.route('/dashboard')
@login_required
@menu_permission_required('/simple/dashboard')
def dashboard():
    """控制台页面，添加各菜单数据统计"""
    # 模板统计
    from app.models.template import Template, TemplateCategory
    template_count = Template.query.count()
    category_count = TemplateCategory.query.count()
    
    # 客户统计
    from app.models.client import Client
    client_count = Client.query.count()
    active_client_count = Client.query.filter(Client.status == True).count()
    
    # 文案统计
    from app.models.content import Content
    content_count = Content.query.count()
    reviewed_content_count = Content.query.filter(Content.internal_review_status == 'final_approved').count()
    
    # 发布统计
    from app.models.publish import PublishRecord
    publish_count = PublishRecord.query.count()
    success_publish_count = PublishRecord.query.filter(PublishRecord.status == 'success').count()
    
    return render_template('main_simple.html',
                          template_count=template_count,
                          category_count=category_count,
                          client_count=client_count,
                          active_client_count=active_client_count,
                          content_count=content_count,
                          reviewed_content_count=reviewed_content_count,
                          publish_count=publish_count,
                          success_publish_count=success_publish_count)





@main_simple_bp.route('/templates')
@login_required
@menu_permission_required('/simple/templates')
def templates():
    """模板管理页面"""
    from app.models.template import Template, TemplateCategory

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    # 获取筛选参数
    category_id = request.args.get('category_id', type=int)
    status = request.args.get('status')

    # 构建查询
    query = Template.query

    # 应用筛选条件
    if category_id:
        query = query.filter(Template.category_id == category_id)

    if status is not None and status != '':
        # Template.status是Boolean类型，'1'表示启用(True)，'0'表示禁用(False)
        status_value = (status == '1')
        query = query.filter(Template.status == status_value)
        print(f"DEBUG - 应用状态筛选: {status_value} (原始值: {status})")

    # 按更新时间排序并分页
    pagination = query.order_by(Template.updated_at.desc()).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # 获取所有分类用于筛选下拉框
    categories = TemplateCategory.query.all()

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，只返回页面内容
        return render_template('template/index_simple.html',
                             templates=pagination.items,
                             pagination=pagination,
                             per_page=per_page,
                             categories=categories)
    else:
        # 普通请求（包括刷新），返回完整页面
        return render_template('main_simple.html',
                             current_page='templates',
                             page_content=render_template('template/index_simple.html',
                                                        templates=pagination.items,
                                                        pagination=pagination,
                                                        per_page=per_page,
                                                        categories=categories))


@main_simple_bp.route('/clients')
@login_required
@menu_permission_required('/simple/clients')
def clients():
    """客户管理页面"""
    from app.models.client import Client

    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    # 限制每页显示数量的范围
    if per_page not in [10, 20, 30, 50, 80, 100]:
        per_page = 20

    # 获取筛选参数
    status = request.args.get('status')
    search = request.args.get('search', '').strip()

    print(f"DEBUG - 客户筛选参数: status={status}, search='{search}'")

    # 构建查询
    query = Client.query

    # 应用筛选条件
    if status is not None and status != '':
        status_bool = (status == '1')
        query = query.filter(Client.status == status_bool)
        print(f"DEBUG - 应用状态筛选: status={status}, status_bool={status_bool}")

    if search:
        query = query.filter(
            db.or_(
                Client.name.contains(search),
                Client.contact.contains(search),
                Client.phone.contains(search),
                Client.email.contains(search)
            )
        )
        print(f"DEBUG - 应用搜索筛选: search='{search}'")

    # 按更新时间排序并分页
    pagination = query.order_by(Client.updated_at.desc()).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # 为每个客户添加文章数量统计
    from app.models.content import Content
    from sqlalchemy import func

    clients_with_stats = []
    for client in pagination.items:
        # 查询该客户的文章总数（未删除的）
        total_content_count = db.session.query(func.count(Content.id)).filter(
            Content.client_id == client.id,
            Content.is_deleted == False
        ).scalar() or 0

        # 查询该客户的已发布文案数量
        published_count = db.session.query(func.count(Content.id)).filter(
            Content.client_id == client.id,
            Content.publish_status == 'published',
            Content.is_deleted == False
        ).scalar() or 0

        # 创建客户对象的副本并添加统计信息
        client_dict = {
            'id': client.id,
            'name': client.name,
            'contact': client.contact,
            'phone': client.phone,
            'email': client.email,
            'need_review': client.need_review,
            'status': client.status,
            'created_at': client.created_at,
            'updated_at': client.updated_at,
            'total_content_count': total_content_count,
            'published_count': published_count,
            # 保留原始对象的其他属性和方法
            '_original_client': client
        }
        clients_with_stats.append(client_dict)

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，只返回页面内容
        return render_template('client/index_simple.html',
                             clients=clients_with_stats,
                             pagination=pagination,
                             per_page=per_page,
                             current_status=status,
                             current_search=search)
    else:
        # 普通请求（包括刷新），返回完整页面
        return render_template('main_simple.html',
                             current_page='clients',
                             page_content=render_template('client/index_simple.html',
                                                        clients=clients_with_stats,
                                                        pagination=pagination,
                                                        per_page=per_page,
                                                        current_status=status,
                                                        current_search=search))


@main_simple_bp.route('/clients/create', methods=['GET', 'POST'])
@login_required
def create_client():
    """创建客户"""
    from app.models.client import Client
    from app.forms.client import ClientForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        form = ClientForm()

        if request.method == 'POST' and form.validate_on_submit():
            try:
                # 创建客户，包含审核超时设置字段
                client = Client(
                    name=form.name.data,
                    contact=form.contact.data,
                    phone=form.phone.data,
                    email=form.email.data,
                    need_review=form.need_review.data,
                    status=form.status.data,
                    daily_content_count=form.daily_content_count.data,
                    interval_min=form.interval_min.data,
                    interval_max=form.interval_max.data,
                    display_start_time=form.display_start_time.data,
                    # 审核超时设置
                    auto_approve_enabled=form.auto_approve_enabled.data,
                    review_timeout_hours=form.review_timeout_hours.data,
                    review_deadline_time=form.review_deadline_time.data,
                    created_at=datetime.now()
                )

                # 将address和remark存储到扩展字段中
                ext_data = {}
                if form.address.data:
                    ext_data['address'] = form.address.data
                if form.remark.data:
                    ext_data['remark'] = form.remark.data

                if ext_data:
                    client.ext_data = ext_data

                # 处理默认值字段 - 直接从request.form获取数据
                default_required_topics_raw = request.form.get('default_required_topics', '')
                if default_required_topics_raw:
                    topics = [t.strip() for t in default_required_topics_raw.replace('\r\n', '\n').split('\n') if t.strip()]
                    client.default_required_topics_list = topics

                default_random_topics_raw = request.form.get('default_random_topics', '')
                if default_random_topics_raw:
                    topics = [t.strip() for t in default_random_topics_raw.replace('\r\n', '\n').split('\n') if t.strip()]
                    client.default_random_topics_list = topics

                default_at_users_raw = request.form.get('default_at_users', '')
                if default_at_users_raw:
                    users = [u.strip() for u in default_at_users_raw.replace('\r\n', '\n').split('\n') if u.strip()]
                    # 确保@符号
                    users = [u if u.startswith('@') else f'@{u}' for u in users]
                    client.default_at_users_list = users

                default_location_raw = request.form.get('default_location', '')
                if default_location_raw:
                    locations = [loc.strip() for loc in default_location_raw.replace('\r\n', '\n').split('\n') if loc.strip()]
                    client.default_location_list = locations

                db.session.add(client)
                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': '客户创建成功',
                    'client_id': client.id
                })

            except Exception as e:
                db.session.rollback()
                return jsonify({
                    'success': False,
                    'message': f'创建失败：{str(e)}'
                }), 500

        # GET请求或表单验证失败，返回表单HTML
        return render_template('client/client_form_modal.html',
                             form=form,
                             edit_mode=False)
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/clients/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(id):
    """编辑客户"""
    from app.models.client import Client
    from app.forms.client import ClientForm

    # 获取客户数据（无论是否为AJAX请求）
    client = Client.query.get_or_404(id)
    form = ClientForm(obj=client)

    # 从扩展字段中加载address和remark
    ext_data = client.ext_data or {}
    if 'address' in ext_data:
        form.address.data = ext_data['address']
    if 'remark' in ext_data:
        form.remark.data = ext_data['remark']

    # 加载默认值字段
    if client.default_required_topics_list:
        form.default_required_topics.data = '\n'.join(client.default_required_topics_list)

    if client.default_random_topics_list:
        form.default_random_topics.data = '\n'.join(client.default_random_topics_list)

    if client.default_at_users_list:
        form.default_at_users.data = '\n'.join(client.default_at_users_list)

    if client.default_location_list:
        form.default_location.data = '\n'.join(client.default_location_list)

    if request.method == 'POST':
        print(f"🔍 编辑客户 - POST请求接收到")
        print(f"🔍 编辑客户 - 请求数据: {dict(request.form)}")
        print(f"🔍 编辑客户 - 表单验证状态: {form.validate_on_submit()}")
        if form.errors:
            print(f"🔍 编辑客户 - 表单错误: {form.errors}")

        if form.validate_on_submit():
            try:
                # 更新客户基本字段
                client.name = form.name.data
                client.contact = form.contact.data
                client.phone = form.phone.data
                client.email = form.email.data
                client.need_review = form.need_review.data
                client.status = form.status.data
                client.daily_content_count = form.daily_content_count.data
                client.interval_min = form.interval_min.data
                client.interval_max = form.interval_max.data
                client.display_start_time = form.display_start_time.data

                # 更新审核超时设置
                client.auto_approve_enabled = form.auto_approve_enabled.data
                client.review_timeout_hours = form.review_timeout_hours.data
                client.review_deadline_time = form.review_deadline_time.data

                client.updated_at = datetime.now()

                # 更新扩展字段
                ext_data = client.ext_data or {}
                if form.address.data:
                    ext_data['address'] = form.address.data
                elif 'address' in ext_data:
                    del ext_data['address']

                if form.remark.data:
                    ext_data['remark'] = form.remark.data
                elif 'remark' in ext_data:
                    del ext_data['remark']

                client.ext_data = ext_data

                # 处理默认值字段
                print(f"🔍 编辑客户 - 接收到的表单数据:")
                print(f"  default_required_topics: {repr(form.default_required_topics.data)}")
                print(f"  default_random_topics: {repr(form.default_random_topics.data)}")
                print(f"  default_at_users: {repr(form.default_at_users.data)}")
                print(f"  default_location: {repr(form.default_location.data)}")

                # 打印原始请求数据
                print(f"🔍 编辑客户 - 原始请求数据:")
                for key, value in request.form.items():
                    if 'default_' in key:
                        print(f"  {key}: {repr(value)}")

                # 直接从request.form获取数据，避免WTForms处理多行数据的问题
                default_required_topics_raw = request.form.get('default_required_topics', '')
                if default_required_topics_raw:
                    # 处理\r\n和\n两种换行符
                    topics = [t.strip() for t in default_required_topics_raw.replace('\r\n', '\n').split('\n') if t.strip()]
                    client.default_required_topics_list = topics
                else:
                    client.default_required_topics_list = []

                default_random_topics_raw = request.form.get('default_random_topics', '')
                if default_random_topics_raw:
                    topics = [t.strip() for t in default_random_topics_raw.replace('\r\n', '\n').split('\n') if t.strip()]
                    client.default_random_topics_list = topics
                else:
                    client.default_random_topics_list = []

                default_at_users_raw = request.form.get('default_at_users', '')
                if default_at_users_raw:
                    users = [u.strip() for u in default_at_users_raw.replace('\r\n', '\n').split('\n') if u.strip()]
                    # 确保@符号
                    users = [u if u.startswith('@') else f'@{u}' for u in users]
                    client.default_at_users_list = users
                else:
                    client.default_at_users_list = []

                default_location_raw = request.form.get('default_location', '')
                if default_location_raw:
                    locations = [loc.strip() for loc in default_location_raw.replace('\r\n', '\n').split('\n') if loc.strip()]
                    client.default_location_list = locations
                else:
                    client.default_location_list = []

                print(f"🔍 编辑客户 - 处理后的数据:")
                print(f"  default_required_topics_list: {client.default_required_topics_list}")
                print(f"  default_random_topics_list: {client.default_random_topics_list}")
                print(f"  default_at_users_list: {client.default_at_users_list}")
                print(f"  default_location_list: {client.default_location_list}")

                db.session.commit()

                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': True,
                        'message': '客户更新成功'
                    })
                else:
                    # 非AJAX请求，重定向到客户列表
                    flash('客户信息更新成功！', 'success')
                    return redirect(url_for('main_simple.clients'))

            except Exception as e:
                db.session.rollback()
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': False,
                        'message': f'更新失败：{str(e)}'
                    }), 500
                else:
                    flash(f'更新失败：{str(e)}', 'error')
                    # 继续显示编辑页面
        else:
            # 表单验证失败
            print(f"🔍 编辑客户 - 表单验证失败: {form.errors}")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'message': '表单验证失败',
                    'errors': form.errors
                }), 400

    # GET请求或表单验证失败，返回表单HTML
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return render_template('client/client_form_modal.html',
                             form=form,
                             edit_mode=True,
                             client=client)
    else:
        # 非AJAX请求，返回完整的编辑页面
        return render_template('main_simple.html',
                             current_page='clients',
                             page_content=render_template('client/edit_client.html',
                                                        form=form,
                                                        client=client))


@main_simple_bp.route('/clients/<int:id>/toggle_status', methods=['POST'])
@login_required
def toggle_client_status(id):
    """切换客户状态"""
    from app.models.client import Client

    try:
        client = Client.query.get_or_404(id)
        client.status = not client.status
        client.updated_at = datetime.now()
        db.session.commit()

        status_text = '启用' if client.status else '禁用'
        return jsonify({
            'success': True,
            'message': f'客户已{status_text}',
            'status': client.status
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'状态更新失败：{str(e)}'
        }), 500


@main_simple_bp.route('/clients/<int:id>/toggle_review', methods=['POST'])
@login_required
def toggle_client_review(id):
    """切换客户审核状态"""
    from app.models.client import Client

    try:
        client = Client.query.get_or_404(id)
        client.need_review = not client.need_review
        client.updated_at = datetime.now()
        db.session.commit()

        review_text = '需要审核' if client.need_review else '无需审核'
        return jsonify({
            'success': True,
            'message': f'审核设置已更新为：{review_text}',
            'need_review': client.need_review
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'审核设置更新失败：{str(e)}'
        }), 500


@main_simple_bp.route('/clients/<int:id>/delete', methods=['POST'])
@login_required
def delete_client(id):
    """删除客户"""
    from app.models.client import Client

    try:
        client = Client.query.get_or_404(id)
        client_name = client.name

        db.session.delete(client)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'客户 "{client_name}" 已删除'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除失败：{str(e)}'
        }), 500


@main_simple_bp.route('/clients/<int:id>/data', methods=['GET'])
@login_required
def get_client_data(id):
    """获取客户数据"""
    from app.models.client import Client

    try:
        client = Client.query.get_or_404(id)

        # 获取扩展字段数据
        ext_data = client.ext_data or {}

        return jsonify({
            'success': True,
            'client': {
                'id': client.id,
                'name': client.name,
                'contact': client.contact,
                'phone': client.phone,
                'email': client.email,
                'address': ext_data.get('address', ''),
                'status': client.status,
                'need_review': client.need_review,
                'daily_content_count': client.daily_content_count,
                'interval_min': client.interval_min,
                'interval_max': client.interval_max,
                'display_start_time': client.display_start_time.strftime('%H:%M') if client.display_start_time else '',
                'remark': ext_data.get('remark', ''),
                'created_at': client.created_at.strftime('%Y-%m-%d %H:%M') if client.created_at else '',
                'updated_at': client.updated_at.strftime('%Y-%m-%d %H:%M') if client.updated_at else ''
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取客户数据失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/defaults')
@login_required
def get_client_defaults(client_id):
    """获取客户默认值"""
    from app.models.client import Client

    try:
        client = Client.query.get_or_404(client_id)

        return jsonify({
            'success': True,
            'data': {
                'required_topics': client.default_required_topics_list,
                'random_topics': client.default_random_topics_list,
                'at_users': client.default_at_users_list,
                'location': client.default_location_list  # 改为返回列表
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取客户默认值失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/content-management')
@login_required
@menu_permission_required('/simple/clients')
def get_client_content_management(client_id):
    """获取客户文章管理数据"""
    try:
        from app.models.client import Client
        from app.models.task import Task
        from app.models.content import Content
        from sqlalchemy import func

        # 检查客户是否存在
        client = Client.query.get_or_404(client_id)

        # 获取客户的任务统计
        tasks_query = Task.query.filter_by(client_id=client_id)
        tasks = tasks_query.all()

        # 统计信息
        total_tasks = len(tasks)
        total_content = 0
        published_content = 0

        task_list = []
        for task in tasks:
            # 获取任务的内容统计
            content_count = Content.query.filter_by(task_id=task.id).count()
            published_count = Content.query.filter_by(task_id=task.id, publish_status='published').count()

            total_content += content_count
            published_content += published_count

            task_list.append({
                'id': task.id,
                'name': task.name,
                'content_count': content_count,
                'published_count': published_count,
                'created_at': task.created_at.strftime('%Y-%m-%d %H:%M') if task.created_at else ''
            })

        # 按创建时间倒序排列
        task_list.sort(key=lambda x: x['created_at'], reverse=True)

        return jsonify({
            'success': True,
            'data': {
                'stats': {
                    'total_tasks': total_tasks,
                    'total_content': total_content,
                    'published_content': published_content
                },
                'tasks': task_list
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取客户文章管理数据失败: {e}")
        return jsonify({'success': False, 'message': '获取数据失败，请稍后重试'})


def delete_content_images(content_id):
    """删除文案的所有图片文件和记录"""
    from app.models.image import ContentImage
    from app.utils.image_handler import ImageUploadHandler
    import os

    try:
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
        image_handler = ImageUploadHandler(upload_folder)

        images = ContentImage.query.filter_by(content_id=content_id, is_deleted=False).all()
        deleted_count = 0

        for image in images:
            try:
                image_handler.delete_image(image.image_path, image.thumbnail_path)
                deleted_count += 1
            except Exception as e:
                current_app.logger.error(f"删除图片文件失败: {e}")

        # 删除图片记录
        ContentImage.query.filter_by(content_id=content_id).delete()

        return deleted_count
    except Exception as e:
        current_app.logger.error(f"删除文案图片失败: {e}")
        return 0


@main_simple_bp.route('/api/clients/<int:client_id>/batch-operation', methods=['POST'])
@login_required
@menu_permission_required('/simple/clients')
def client_batch_operation(client_id):
    """客户文章批量操作"""
    try:
        from app.models.client import Client
        from app.models.task import Task
        from app.models.content import Content
        from app import db

        # 检查客户是否存在
        client = Client.query.get_or_404(client_id)

        data = request.get_json()
        operation = data.get('operation')
        task_ids = data.get('task_ids', [])

        if operation == 'delete-content':
            # 删除该客户的所有文章内容（包括相关记录和图片文件）
            from app.models.publish import PublishRecord
            from app.models.content import ContentHistory, RejectionReason
            from app.models.image import ContentImage
            from app.utils.image_handler import ImageUploadHandler
            import os

            # 初始化图片处理器
            upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
            image_handler = ImageUploadHandler(upload_folder)

            tasks = Task.query.filter_by(client_id=client_id).all()
            deleted_count = 0
            deleted_records = 0
            deleted_images = 0

            for task in tasks:
                contents = Content.query.filter_by(task_id=task.id).all()
                for content in contents:
                    # 删除图片文件和记录
                    deleted_images += delete_content_images(content.id)

                    # 删除发布记录
                    PublishRecord.query.filter_by(content_id=content.id).delete()
                    # 删除内容历史
                    ContentHistory.query.filter_by(content_id=content.id).delete()
                    # 删除驳回原因
                    RejectionReason.query.filter_by(content_id=content.id).delete()
                    deleted_records += 1

                # 删除内容本身
                content_count = len(contents)
                Content.query.filter_by(task_id=task.id).delete()
                deleted_count += content_count

            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'成功删除 {deleted_count} 篇文章、{deleted_images} 张图片及相关记录'
            })

        elif operation == 'delete-all-tasks':
            # 删除该客户的所有任务及其文章（按正确顺序删除）
            from app.models.task import Batch

            tasks = Task.query.filter_by(client_id=client_id).all()
            deleted_tasks = len(tasks)
            deleted_content = 0
            deleted_batches = 0
            deleted_images = 0

            for task in tasks:
                # 1. 先删除该任务下的所有文章及相关记录
                from app.models.publish import PublishRecord
                from app.models.content import ContentHistory, RejectionReason

                contents = Content.query.filter_by(task_id=task.id).all()
                for content in contents:
                    # 删除图片文件和记录
                    deleted_images += delete_content_images(content.id)

                    # 删除发布记录
                    PublishRecord.query.filter_by(content_id=content.id).delete()
                    # 删除内容历史
                    ContentHistory.query.filter_by(content_id=content.id).delete()
                    # 删除驳回原因
                    RejectionReason.query.filter_by(content_id=content.id).delete()

                content_count = len(contents)
                Content.query.filter_by(task_id=task.id).delete()
                deleted_content += content_count

                # 2. 再删除该任务下的所有批次
                batch_count = Batch.query.filter_by(task_id=task.id).count()
                Batch.query.filter_by(task_id=task.id).delete()
                deleted_batches += batch_count

                # 3. 最后删除任务本身
                db.session.delete(task)

            db.session.commit()

            return jsonify({
                'success': True,
                'message': f'成功删除 {deleted_tasks} 个任务、{deleted_batches} 个批次、{deleted_content} 篇文章和 {deleted_images} 张图片及相关记录'
            })

        elif operation == 'delete-selected-tasks':
            # 删除选中的任务（按正确顺序删除）
            from app.models.task import Batch

            if not task_ids:
                return jsonify({'success': False, 'message': '未选择要删除的任务'})

            deleted_tasks = 0
            deleted_content = 0
            deleted_batches = 0
            deleted_images = 0

            for task_id in task_ids:
                task = Task.query.filter_by(id=task_id, client_id=client_id).first()
                if task:
                    # 1. 先删除该任务下的所有文章及其相关记录
                    from app.models.publish import PublishRecord
                    from app.models.content import ContentHistory, RejectionReason

                    contents = Content.query.filter_by(task_id=task.id).all()
                    content_count = len(contents)

                    for content in contents:
                        # 删除图片文件和记录
                        deleted_images += delete_content_images(content.id)

                        # 删除发布记录
                        PublishRecord.query.filter_by(content_id=content.id).delete()
                        # 删除内容历史
                        ContentHistory.query.filter_by(content_id=content.id).delete()
                        # 删除驳回原因
                        RejectionReason.query.filter_by(content_id=content.id).delete()

                    # 删除文案本身
                    Content.query.filter_by(task_id=task.id).delete()
                    deleted_content += content_count

                    # 2. 再删除该任务下的所有批次
                    batch_count = Batch.query.filter_by(task_id=task.id).count()
                    Batch.query.filter_by(task_id=task.id).delete()
                    deleted_batches += batch_count

                    # 3. 最后删除任务本身
                    db.session.delete(task)
                    deleted_tasks += 1

            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'成功删除 {deleted_tasks} 个任务、{deleted_batches} 个批次、{deleted_content} 篇文章和 {deleted_images} 张图片及相关记录'
            })

        elif operation == 'delete-task':
            # 删除单个任务（按正确顺序删除）
            from app.models.task import Batch

            if not task_ids or len(task_ids) != 1:
                return jsonify({'success': False, 'message': '任务ID无效'})

            task_id = task_ids[0]
            task = Task.query.filter_by(id=task_id, client_id=client_id).first()

            if not task:
                return jsonify({'success': False, 'message': '任务不存在'})

            # 1. 先删除该任务下的所有文章及其相关记录
            from app.models.publish import PublishRecord
            from app.models.content import ContentHistory, RejectionReason

            contents = Content.query.filter_by(task_id=task.id).all()
            content_count = len(contents)
            deleted_images = 0

            for content in contents:
                # 删除图片文件和记录
                deleted_images += delete_content_images(content.id)

                # 删除发布记录
                PublishRecord.query.filter_by(content_id=content.id).delete()
                # 删除内容历史
                ContentHistory.query.filter_by(content_id=content.id).delete()
                # 删除驳回原因
                RejectionReason.query.filter_by(content_id=content.id).delete()

            # 删除文案本身
            Content.query.filter_by(task_id=task.id).delete()

            # 2. 再删除该任务下的所有批次
            batch_count = Batch.query.filter_by(task_id=task.id).count()
            Batch.query.filter_by(task_id=task.id).delete()

            # 3. 最后删除任务本身
            task_name = task.name
            db.session.delete(task)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': f'成功删除任务"{task_name}"、{batch_count} 个批次、{content_count} 篇文章和 {deleted_images} 张图片及相关记录'
            })

        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'})

    except Exception as e:
        current_app.logger.error(f"客户文章批量操作失败: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'message': '操作失败，请稍后重试'})


@main_simple_bp.route('/content', methods=['GET', 'POST'])
@login_required
@menu_permission_required('/simple/content')
def content():
    """内容生成页面 - 完整的批量生成功能"""
    # 导入必要的模型
    from app.models.task import Task, Batch

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，调用完整的批量生成功能
        # 直接使用老版本的完整逻辑
        form = GenerateContentForm()

        # 初始化客户选择项
        form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]

        # 初始化模板分类选择项
        form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]

        # 根据选择的客户加载任务
        if request.method == 'GET':
            # 获取客户ID，如果没有提供，则使用第一个客户
            client_id = request.args.get('client_id')
            if not client_id and form.client_id.choices:
                client_id = form.client_id.choices[0][0]

            if client_id:
                client_id = int(client_id)
                tasks = Task.query.filter_by(client_id=client_id).all()
                form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]

                # 设置默认任务名称为"年年年年年月月日日任务"格式
                now = datetime.now()
                default_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
                form.new_task_name.data = default_task_name

                # 检查默认任务名称是否已存在
                existing_task = Task.query.filter_by(
                    client_id=client_id,
                    name=default_task_name
                ).first()

                if existing_task:
                    # 如果存在同名任务，获取该任务下的批次数量
                    batch_count = Batch.query.filter_by(task_id=existing_task.id).count()
                    form.batch_name.data = f'批次 {batch_count + 1}'
                else:
                    # 新任务的第一个批次
                    form.batch_name.data = '批次 1'
            else:
                form.task_id.choices = [(0, '-- 创建新任务 --')]
                # 设置默认任务名称为"年年年年年月月日日任务"格式
                now = datetime.now()
                form.new_task_name.data = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
                form.batch_name.data = '批次 1'

        # 如果是POST请求，处理表单提交
        if request.method == 'POST':
            # 检查是否是AJAX请求
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'

            # 移除前端验证检查，直接进行后端验证

            # 特殊处理：如果task_id为0（创建新任务），确保new_task_name有值
            if 'task_id' in request.form and request.form['task_id'] == '0':
                # 获取或生成任务名称
                new_task_name = request.form.get('new_task_name')
                if not new_task_name:
                    # 设置默认任务名称为"年年年年年月月日日任务"格式
                    now = datetime.now()
                    new_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
                    form.new_task_name.data = new_task_name
                else:
                    form.new_task_name.data = new_task_name

                # 检查是否已存在同名任务
                client_id = request.form.get('client_id')
                if client_id:
                    existing_task = Task.query.filter_by(
                        client_id=int(client_id),
                        name=new_task_name
                    ).first()

                    if existing_task:
                        # 如果存在同名任务，获取该任务下的批次数量
                        batch_count = Batch.query.filter_by(task_id=existing_task.id).count()
                        form.batch_name.data = f'批次 {batch_count + 1}'
                    else:
                        # 新任务的第一个批次
                        form.batch_name.data = '批次 1'

            # 确保提交的task_id在choices列表中
            task_id = request.form.get('task_id')
            if task_id and int(task_id) > 0:
                # 获取任务信息，确保它存在
                task = Task.query.get(int(task_id))
                if task:
                    # 更新choices列表，确保包含当前选择的任务
                    client_id = request.form.get('client_id')
                    if client_id:
                        tasks = Task.query.filter_by(client_id=int(client_id)).all()
                        form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]
                        # 如果当前task_id不在列表中，添加它
                        if not any(t[0] == int(task_id) for t in form.task_id.choices):
                            form.task_id.choices.append((task.id, task.name))

                    # 设置批次名称
                    batch_count = Batch.query.filter_by(task_id=int(task_id)).count()
                    form.batch_name.data = f'批次 {batch_count + 1}'

            # 确保所有选择字段都有有效的选项
            if not form.client_id.choices:
                form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]

            if not form.template_category_id.choices:
                form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]

            # 确保task_id字段有有效的选项
            if not form.task_id.choices:
                client_id = request.form.get('client_id')
                if client_id:
                    tasks = Task.query.filter_by(client_id=int(client_id)).all()
                    form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]
                else:
                    form.task_id.choices = [(0, '-- 创建新任务 --')]

            # 确保publish_priority字段有有效的选项
            if not form.publish_priority.choices:
                form.publish_priority.choices = [
                    ('high', '高优先级'),
                    ('normal', '普通优先级'),
                    ('low', '低优先级')
                ]

            # 如果表单验证通过，处理文案生成
            if form.validate_on_submit():
                print("=== 表单验证通过，开始处理文案生成 ===")
                print(f"表单数据: {request.form}")
                print(f"表单错误: {form.errors}")
                try:
                    # 使用新的文案生成逻辑
                    from app.utils.content_generator import generate_contents
                    import uuid

                    # 获取表单数据
                    client_id = form.client_id.data
                    task_id = form.task_id.data
                    template_category_id = form.template_category_id.data
                    count = form.count.data

                    print(f"DEBUG - 获取到的表单数据:")
                    print(f"  client_id: {client_id}")
                    print(f"  task_id: {task_id}")
                    print(f"  template_category_id: {template_category_id}")
                    print(f"  count: {count}")

                    # 处理任务
                    if task_id == 0:
                        # 创建新任务或使用现有任务
                        new_task_name = form.new_task_name.data
                        print(f"DEBUG - 新任务名称: {new_task_name}")
                        
                        if not new_task_name:
                            print("DEBUG - 新任务名称为空，返回错误")
                            return jsonify({
                                'success': False,
                                'message': '请输入新任务名称'
                            })

                        # 检查任务名称是否已存在
                        existing_task = Task.query.filter_by(
                            client_id=client_id,
                            name=new_task_name
                        ).first()

                        if existing_task:
                            # 使用现有任务
                            task_id = existing_task.id
                            print(f"使用现有任务: {new_task_name} (ID: {task_id})")
                        else:
                            # 创建新任务
                            task = Task(
                                name=new_task_name,
                                client_id=client_id,
                                created_by=current_user.id,
                                created_at=datetime.now()
                            )
                            db.session.add(task)
                            db.session.flush()  # 获取任务ID
                            task_id = task.id
                            print(f"创建新任务: {new_task_name} (ID: {task_id})")

                    # 创建批次（自动递增批次编号）
                    # 查询该任务下现有的批次数量
                    existing_batch_count = Batch.query.filter_by(task_id=task_id).count()
                    batch_number = existing_batch_count + 1

                    batch = Batch(
                        task_id=task_id,
                        name=f"批次{batch_number}",
                        created_by=current_user.id,
                        created_at=datetime.now()
                    )
                    db.session.add(batch)
                    db.session.flush()  # 获取批次ID
                    print(f"创建批次: {batch.name} (ID: {batch.id}) for 任务ID: {task_id}")

                    # 准备生成参数
                    keywords = {}

                    # 从keywords字段解析标记替换数据
                    keywords_data = form.keywords.data or ""
                    print(f"DEBUG - 接收到的关键词数据: {keywords_data}")

                    if keywords_data.strip():
                        for line in keywords_data.strip().split('\n'):
                            if ':' in line:
                                mark, keywords_str = line.split(':', 1)
                                mark = mark.strip()
                                # 支持逗号分隔的关键词
                                if ',' in keywords_str:
                                    keyword_list = [k.strip() for k in keywords_str.split(',') if k.strip()]
                                else:
                                    keyword_list = [keywords_str.strip()] if keywords_str.strip() else []

                                if keyword_list:
                                    keywords[mark] = keyword_list
                                    print(f"DEBUG - 解析标记 {mark}: {keyword_list}")

                    print(f"DEBUG - 最终解析的关键词字典: {keywords}")

                    # 处理话题、@用户、定位等
                    # process_topics函数期望字符串参数，所以直接传递原始数据
                    required_topics = form.required_topics.data if form.required_topics.data else ""
                    random_topics = form.random_topics.data if form.random_topics.data else ""
                    at_users = form.at_users.data if form.at_users.data else ""
                    location = form.location.data if form.location.data else None

                    print(f"DEBUG - 处理后的数据:")
                    print(f"  required_topics: {required_topics}")
                    print(f"  random_topics: {random_topics}")
                    print(f"  at_users: {at_users}")
                    print(f"  location: {location}")

                    # 生成文案
                    print("DEBUG - 开始调用generate_contents函数")
                    contents = generate_contents(
                        client_id=client_id,
                        task_id=task_id,
                        batch_id=batch.id,
                        template_category_id=template_category_id,
                        count=count,
                        keywords=keywords,  # 直接传递解析好的字典
                        required_topics=required_topics,
                        random_topics=random_topics,
                        max_topics_count=form.max_topics_count.data or 10,
                        at_users=at_users,
                        random_at_users_count=form.random_at_users_count.data or 1,
                        location=location,
                        publish_priority=form.publish_priority.data or 1,
                        avoid_duplicates=form.avoid_duplicates.data or False,
                        allow_template_duplicate=int(form.allow_template_duplicate.data) if form.allow_template_duplicate.data else 1,
                        current_user=current_user
                    )

                    print(f"DEBUG - generate_contents返回结果: {len(contents)} 篇文案")

                    # 提交数据库事务
                    db.session.commit()
                    print("DEBUG - 数据库事务提交成功")

                    # 返回成功响应
                    if is_ajax:
                        response_data = {
                            'success': True,
                            'message': f'文案生成成功！共生成了 {len(contents)} 篇文案，可以到初审文案页面查看生成的内容',
                            'clear_form': True,
                            'generated_count': len(contents),
                            'batch_id': batch.id,
                            'task_id': task_id
                        }
                        print(f"DEBUG - 返回AJAX响应: {response_data}")
                        return jsonify(response_data)
                    else:
                        # 非AJAX请求，返回成功页面
                        return render_template('content/generate_simple.html',
                                             form=form,
                                             success_message=f'文案生成成功！共生成了 {len(contents)} 篇文案，可以到初审文案页面查看生成的内容')

                except Exception as e:
                    db.session.rollback()
                    print(f"生成文案出错: {str(e)}")
                    import traceback
                    traceback.print_exc()

                    if is_ajax:
                        return jsonify({
                            'success': False,
                            'message': f'生成文案失败: {str(e)}'
                        }), 500
                    else:
                        # 非AJAX请求，显示错误页面
                        return render_template('content/generate_simple.html',
                                             form=form,
                                             error_message=f'生成文案失败: {str(e)}')
            else:
                # 表单验证失败
                print("=== 表单验证失败 ===")
                print(f"表单错误: {form.errors}")
                print(f"表单数据: {request.form}")
                
                if is_ajax:
                    # 收集表单错误信息
                    error_messages = []
                    for field, errors in form.errors.items():
                        for error in errors:
                            error_messages.append(f'{field}: {error}')

                    error_response = {
                        'success': False,
                        'message': '表单验证失败: ' + '; '.join(error_messages) if error_messages else '请检查输入信息'
                    }
                    print(f"DEBUG - 返回验证失败响应: {error_response}")
                    return jsonify(error_response)
                # 非AJAX请求继续正常流程

        # 返回页面内容
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # AJAX请求，只返回页面内容
            return render_template('content/generate_simple.html', form=form)
        else:
            # 普通请求（包括刷新），返回完整页面
            return render_template('main_simple.html',
                                 current_page='content',
                                 page_content=render_template('content/generate_simple.html', form=form))
    else:
        # 处理非AJAX的GET请求
        form = GenerateContentForm()
        form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
        form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]

        # 初始化任务选择框 - 如果有客户，加载第一个客户的任务
        if form.client_id.choices:
            client_id = form.client_id.choices[0][0]
            tasks = Task.query.filter_by(client_id=client_id).all()
            form.task_id.choices = [(0, '-- 创建新任务 --')] + [(t.id, t.name) for t in tasks]

            # 设置默认任务名称
            now = datetime.now()
            default_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
            form.new_task_name.data = default_task_name
        else:
            # 没有客户时，只有创建新任务选项
            form.task_id.choices = [(0, '-- 创建新任务 --')]

        # 返回完整页面
        return render_template('main_simple.html',
                             current_page='content',
                             page_content=render_template('content/generate_simple.html', form=form))


@main_simple_bp.route('/contents/get-clients')
@login_required
def get_clients():
    """获取客户列表"""
    from app.models.client import Client
    clients = Client.query.filter_by(status=True).all()
    client_list = [{'id': client.id, 'name': client.name} for client in clients]

    return jsonify({
        'success': True,
        'clients': client_list
    })


@main_simple_bp.route('/contents/get-tasks/<int:client_id>')
@login_required
def get_tasks(client_id):
    """获取客户的任务列表"""
    tasks = Task.query.filter_by(client_id=client_id).all()
    task_list = [{'id': task.id, 'name': task.name} for task in tasks]

    return jsonify({
        'success': True,
        'tasks': task_list
    })


@main_simple_bp.route('/review-content')
@login_required
@menu_permission_required('/simple/review-content')
def review_content_page():
    """初审文案客户列表页面"""
    from app.models.client import Client
    from app.models.content import Content
    from sqlalchemy import func

    # 获取有待审核文章的客户列表
    # 查询条件：草稿、内部驳回、客户驳回状态的文章
    clients_with_pending = db.session.query(
        Client.id,
        Client.name,
        func.count(Content.id).label('pending_count')
    ).join(Content, Client.id == Content.client_id).filter(
        Content.is_deleted == False,
        db.or_(
            # 普通草稿
            db.and_(
                Content.workflow_status == 'draft',
                Content.client_review_status != 'rejected',
                Content.internal_review_status != 'rejected'
            ),
            # 内部驳回
            db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
            db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text'),
            db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_both'),
            # 客户驳回
            db.and_(Content.workflow_status == 'draft', Content.client_review_status == 'rejected')
        )
    ).group_by(Client.id, Client.name).order_by(Client.name).all()

    return render_template('main_simple.html',
                         current_page='review-content',
                         page_content=render_template('content/review_content_clients.html',
                                                    clients_with_pending=clients_with_pending))



@main_simple_bp.route('/review-content/client/<int:client_id>')
@login_required
@menu_permission_required('/simple/review-content')
def review_client_content_detail(client_id):
    """客户文案初审详情页面"""
    from app.forms.content import ContentFilterForm
    from app.models.image import ContentImage

    # 获取客户信息
    client = Client.query.get_or_404(client_id)

    form = ContentFilterForm()

    # 初始化表单选择项 - 只显示当前客户的任务
    client_tasks = Task.query.filter_by(client_id=client_id).all()
    form.task_id.choices = [(0, '全部任务')] + [(t.id, t.name) for t in client_tasks]

    # 修改状态选择项，只显示初审相关的3种状态
    form.status.choices = [
        ('', '全部状态'),
        ('draft', '草稿'),
        ('pending_review', '内部驳回'),
        ('client_rejected', '客户驳回')
    ]

    # 获取筛选参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    task_id = request.args.get('task_id', 0, type=int)
    batch_id = request.args.get('batch_id', 0, type=int)
    status = request.args.get('status', '')
    date_from = request.args.get('date_from') or ''
    date_to = request.args.get('date_to') or ''
    search = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'updated_at')
    sort_order = request.args.get('sort_order', 'desc')

    # 构建查询
    query = Content.query

    # 过滤已删除的文案
    query = query.filter(Content.is_deleted == False)

    # 强制只查询指定客户的内容
    query = query.filter(Content.client_id == client_id)

    if task_id > 0:
        query = query.filter(Content.task_id == task_id)

    if batch_id > 0:
        query = query.filter(Content.batch_id == batch_id)

    # 应用状态筛选
    if status:
        if status == 'draft':
            # 普通草稿
            query = query.filter(
                Content.workflow_status == 'draft',
                Content.client_review_status != 'rejected',
                Content.internal_review_status != 'rejected'
            )
        elif status == 'pending_review':
            # 内部驳回（包括普通内部驳回和终审驳回的文案问题）
            query = query.filter(
                db.or_(
                    # 普通内部驳回
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                    # 终审驳回的文案问题（包括新的精确状态）
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text'),
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_both'),
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text_ok')
                )
            )
        elif status == 'client_rejected':
            # 客户驳回
            query = query.filter(
                db.or_(
                    # 客户驳回（文案问题）
                    db.and_(
                        Content.workflow_status == 'draft',
                        Content.client_review_status == 'rejected',
                        Content.internal_review_status == 'client_rej_text'
                    ),
                    # 客户驳回（两者都有问题）- 文案未完成时显示
                    db.and_(
                        Content.workflow_status == 'draft',
                        Content.client_review_status == 'rejected',
                        Content.internal_review_status == 'client_rej_both',
                        Content.content_completed != 1
                    )
                )
            )
    else:
        # 如果没有指定状态，显示所有初审页面相关的文案
        query = query.filter(
            db.or_(
                # 普通草稿
                db.and_(
                    Content.workflow_status == 'draft',
                    Content.client_review_status != 'rejected',
                    Content.internal_review_status != 'rejected'
                ),
                # 内部驳回
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                # 客户驳回（文案问题）
                db.and_(
                    Content.workflow_status == 'draft',
                    Content.client_review_status == 'rejected',
                    Content.internal_review_status == 'client_rej_text'
                ),
                # 客户驳回（两者都有问题）- 文案未完成时显示
                db.and_(
                    Content.workflow_status == 'draft',
                    Content.client_review_status == 'rejected',
                    Content.internal_review_status == 'client_rej_both',
                    Content.content_completed != 1
                ),
                # 终审驳回的文案问题（包括新的精确状态）
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text'),
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_both'),
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text_ok')
            )
        )

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Content.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            date_to_obj = date_to_obj.replace(hour=23, minute=59, second=59)
            query = query.filter(Content.created_at <= date_to_obj)
        except ValueError:
            pass

    if search:
        query = query.filter(
            db.or_(
                Content.title.contains(search),
                Content.content.contains(search)
            )
        )

    # 排序
    if sort_by == 'created_at':
        if sort_order == 'desc':
            query = query.order_by(Content.created_at.desc())
        else:
            query = query.order_by(Content.created_at.asc())
    elif sort_by == 'updated_at':
        if sort_order == 'desc':
            query = query.order_by(Content.updated_at.desc())
        else:
            query = query.order_by(Content.updated_at.asc())
    elif sort_by == 'title':
        if sort_order == 'desc':
            query = query.order_by(Content.title.desc())
        else:
            query = query.order_by(Content.title.asc())

    # 分页
    contents = query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

    # 为每个内容获取图片信息
    content_data = []
    for content in contents.items:
        images = ContentImage.get_by_content(content.id)
        content_data.append({
            'content': content,
            'images': images,
            'image_count': len(images)
        })

    # 获取有初审数据的客户和任务列表用于筛选
    from sqlalchemy import and_

    # 只显示有初审相关文案的客户（草稿、内部驳回、客户驳回）
    clients_with_content = db.session.query(Client).join(Content).filter(
        and_(
            db.or_(
                # 普通草稿
                db.and_(
                    Content.workflow_status == 'draft',
                    Content.client_review_status != 'rejected',
                    Content.internal_review_status != 'rejected'
                ),
                # 内部驳回
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                Content.workflow_status == 'pending_review',
                # 客户驳回
                db.and_(Content.workflow_status == 'draft', Content.client_review_status == 'rejected')
            ),
            Content.is_deleted == False,
            Client.status == True
        )
    ).distinct().order_by(Client.name).all()

    # 根据客户筛选显示任务
    if client_id > 0:
        # 如果选择了特定客户，只显示该客户下有初审相关文案的任务
        tasks_with_content = db.session.query(Task).join(Content).filter(
            and_(
                Content.client_id == client_id,
                db.or_(
                    # 普通草稿
                    db.and_(
                        Content.workflow_status == 'draft',
                        Content.client_review_status != 'rejected',
                        Content.internal_review_status != 'rejected'
                    ),
                    # 内部驳回
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                    Content.workflow_status == 'pending_review',
                    # 客户驳回
                    db.and_(Content.workflow_status == 'draft', Content.client_review_status == 'rejected')
                ),
                Content.is_deleted == False
            )
        ).distinct().order_by(Task.name).all()
    else:
        # 如果选择"全部客户"，显示所有有初审相关文案的任务
        tasks_with_content = db.session.query(Task).join(Content).filter(
            and_(
                db.or_(
                    # 普通草稿
                    db.and_(
                        Content.workflow_status == 'draft',
                        Content.client_review_status != 'rejected',
                        Content.internal_review_status != 'rejected'
                    ),
                    # 内部驳回
                    db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                    Content.workflow_status == 'pending_review',
                    # 客户驳回
                    db.and_(Content.workflow_status == 'draft', Content.client_review_status == 'rejected')
                ),
                Content.is_deleted == False
            )
        ).distinct().order_by(Task.name).all()

    # 计算各状态的数量统计（只统计初审页面相关的3种状态）
    status_counts = {
        # 普通草稿：workflow_status为draft，且不是客户驳回也不是内部驳回，也不是终审驳回
        'draft': Content.query.filter(
            Content.workflow_status == 'draft',
            Content.client_review_status != 'rejected',
            Content.internal_review_status != 'rejected',
            # 排除终审驳回的状态
            ~Content.internal_review_status.in_(['final_rej_text', 'final_rej_both', 'final_rej_text_ok']),
            Content.is_deleted == False
        ).count(),
        # 内部驳回：包括普通内部驳回和终审驳回的文案问题
        'pending_review': Content.query.filter(
            db.or_(
                # 普通内部驳回
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'rejected'),
                # 终审驳回的文案问题（包括新的精确状态）
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text'),
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_both'),
                db.and_(Content.workflow_status == 'draft', Content.internal_review_status == 'final_rej_text_ok')
            ),
            Content.is_deleted == False
        ).count(),
        # 客户驳回：workflow_status为draft且client_review_status为rejected
        'client_rejected': Content.query.filter(
            Content.workflow_status == 'draft',
            Content.client_review_status == 'rejected',
            Content.is_deleted == False
        ).count()
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回客户特定的页面内容
        return render_template('content/review_client_content.html',
                             contents=contents.items,
                             pagination=contents,
                             client=client,
                             tasks=client_tasks,
                             task_id=task_id,
                             batch_id=batch_id,
                             status=status,
                             search=search,
                             status_counts=status_counts)
    else:
        # 普通请求（包括刷新），返回完整页面
        return render_template('main_simple.html',
                             current_page='review-content',
                             page_content=render_template('content/review_client_content.html',
                                                        contents=contents.items,
                                                        pagination=contents,
                                                        client=client,
                                                        tasks=client_tasks,
                                                        task_id=task_id,
                                                        batch_id=batch_id,
                                                        status=status,
                                                        search=search,
                                                        status_counts=status_counts))



@main_simple_bp.route('/contents/get-batches/<int:task_id>')
@login_required
def get_batches(task_id):
    """获取指定任务的批次列表"""
    try:
        batches = Batch.query.filter_by(task_id=task_id).order_by(Batch.created_at.desc()).all()

        batch_list = []
        for batch in batches:
            batch_list.append({
                'id': batch.id,
                'name': batch.name
            })

        return jsonify({
            'success': True,
            'batches': batch_list
        })
    except Exception as e:
        print(f"Error getting batches: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@main_simple_bp.route('/contents/<int:content_id>/review', methods=['POST'])
@login_required
@csrf.exempt
def review_content(content_id):
    """单个文案初审"""
    try:
        content = Content.query.get_or_404(content_id)

        # 检查当前状态是否可以审核
        valid_states = ['draft', 'pending_review']
        if content.workflow_status not in valid_states:
            return jsonify({'success': False, 'message': '当前文案状态不允许审核'}), 400

        # 获取表单数据
        status = request.form.get('status')
        reason = request.form.get('reason', '')

        if status == 'approved':
            # 初审通过 - 检查是否是驳回状态
            import logging
            logging.warning(f'DEBUG: main_simple.py 单个审核 - workflow_status={content.workflow_status}, internal_review_status={content.internal_review_status}')

            if content.internal_review_status == 'final_rej_text_ok':
                logging.warning(f'DEBUG: main_simple.py 检测到 final_rej_text_ok 状态，进入驳回处理分支')
                # 驳回状态：图片已修复，文案审核通过，检查是否都完成
                if content.image_completed == 1:
                    # 图片已完成，文案也审核通过，直接进入终审
                    content.workflow_status = 'final_review'
                    content.internal_review_status = 'pending'
                    content.content_completed = 1
                    content.review_notes = '驳回状态：图片和文案都已完成，进入终审'
                    logging.warning(f'DEBUG: 图片已完成，文案审核通过，进入终审')
                else:
                    # 图片未完成，文案审核通过，等待图片处理
                    content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                    content.internal_review_status = 'final_rej_img'  # 改为等待图片处理
                    content.content_completed = 1
                    content.review_notes = '驳回状态：文案审核通过，等待图片处理'
                    logging.warning(f'DEBUG: 图片未完成，文案审核通过，等待图片处理')
            elif content.internal_review_status in ['final_rej_text', 'final_rej_both']:
                logging.warning(f'DEBUG: main_simple.py 检测到其他驳回状态 {content.internal_review_status}，进入驳回处理分支')
                # 驳回状态：文案审核通过，检查图片状态
                if content.image_completed == 1:
                    # 图片已完成，文案审核通过，直接进入终审
                    content.workflow_status = 'final_review'
                    content.internal_review_status = 'pending'
                    content.content_completed = 1
                    content.review_notes = '驳回状态：图片和文案都已完成，进入终审'
                    logging.warning(f'DEBUG: 图片已完成，文案审核通过，进入终审')
                else:
                    # 图片未完成，文案审核通过，等待图片处理
                    content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                    content.internal_review_status = 'final_rej_img'  # 改为等待图片处理
                    content.content_completed = 1
                    content.review_notes = '驳回状态：文案审核通过，等待图片处理'
                    logging.warning(f'DEBUG: 图片未完成，文案审核通过，等待图片处理')
            elif content.internal_review_status in ['client_rej_text', 'client_rej_both']:
                logging.warning(f'DEBUG: main_simple.py 检测到客户驳回状态 {content.internal_review_status}，进入客户驳回处理分支')
                # 客户驳回状态：文案审核通过，检查图片状态
                if content.image_completed == 1:
                    # 图片已完成，文案也审核通过，直接进入终审
                    content.workflow_status = 'final_review'
                    content.client_review_status = 'pending'  # 重置客户审核状态
                    content.internal_review_status = 'pending'
                    content.content_completed = 1
                    content.review_notes = '客户驳回状态：图片和文案都已完成，进入终审'
                    logging.warning(f'DEBUG: 客户驳回-图片已完成，文案审核通过，进入终审')
                else:
                    # 图片未完成，文案审核通过，等待图片处理
                    content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                    content.internal_review_status = 'client_rej_img'  # 改为等待图片处理
                    content.content_completed = 1
                    content.review_notes = '客户驳回状态：文案审核通过，等待图片处理'
                    logging.warning(f'DEBUG: 客户驳回-图片未完成，文案审核通过，等待图片处理')
            else:
                logging.warning(f'DEBUG: main_simple.py 进入正常初审分支')
                # 正常初审通过 - 检查是否已有图片
                if content.image_urls and content.image_urls.strip() and content.image_urls != '[]':
                    # 已有图片，直接进入终审
                    content.workflow_status = 'final_review'
                    content.internal_review_status = 'pending'
                    content.review_notes = '初审通过，已有图片，直接进入终审'
                else:
                    # 没有图片，需要上传图片
                    content.workflow_status = 'first_reviewed'
                    content.internal_review_status = 'first_approved'
                    content.review_notes = '初审通过'
        elif status == 'rejected':
            # 拒绝文案
            content.workflow_status = 'draft'
            content.review_notes = f'初审拒绝: {reason}'
        else:
            return jsonify({'success': False, 'message': '无效的操作'}), 400

        # 记录审核人和审核时间
        content.reviewer_id = current_user.id
        content.review_time = datetime.now()
        content.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '操作成功'
        })
    except Exception as e:
        db.session.rollback()
        print(f"Error reviewing content: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@main_simple_bp.route('/contents/batch-action', methods=['POST'])
@login_required
def batch_action():
    """批量文案操作"""
    try:
        action = request.form.get('action')
        content_ids_str = request.form.get('content_ids', '')
        status = request.form.get('status')
        reason = request.form.get('reason', '')

        if not content_ids_str:
            return jsonify({'success': False, 'message': '请选择要操作的文案'}), 400

        content_ids = [int(id.strip()) for id in content_ids_str.split(',') if id.strip()]

        if action == 'review':
            contents = Content.query.filter(Content.id.in_(content_ids)).all()

            count = 0
            for content in contents:
                # 检查状态是否允许审核
                valid_states = ['draft', 'pending_review']
                if content.workflow_status not in valid_states:
                    continue

                if status == 'approved':
                    # 批量初审通过 - 检查是否是驳回状态
                    import logging
                    logging.warning(f'DEBUG: main_simple.py 批量审核 - workflow_status={content.workflow_status}, internal_review_status={content.internal_review_status}')

                    if content.internal_review_status == 'final_rej_text_ok':
                        logging.warning(f'DEBUG: main_simple.py 批量审核检测到 final_rej_text_ok 状态，进入驳回处理分支')
                        # 驳回状态：图片已修复，文案审核通过，检查是否都完成
                        if content.image_completed == 1:
                            # 图片已完成，文案也审核通过，直接进入终审
                            content.workflow_status = 'final_review'
                            content.internal_review_status = 'pending'
                            content.content_completed = 1
                            content.review_notes = '批量驳回状态：图片和文案都已完成，进入终审'
                            logging.warning(f'DEBUG: 批量审核-图片已完成，文案审核通过，进入终审')
                        else:
                            # 图片未完成，文案审核通过，等待图片处理
                            content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                            content.internal_review_status = 'final_rej_img'  # 改为等待图片处理
                            content.content_completed = 1
                            content.review_notes = '批量驳回状态：文案审核通过，等待图片处理'
                            logging.warning(f'DEBUG: 批量审核-图片未完成，文案审核通过，等待图片处理')
                    elif content.internal_review_status in ['final_rej_text', 'final_rej_both']:
                        logging.warning(f'DEBUG: main_simple.py 批量审核检测到其他驳回状态 {content.internal_review_status}，进入驳回处理分支')
                        # 驳回状态：文案审核通过，检查图片状态
                        if content.image_completed == 1:
                            # 图片已完成，文案审核通过，直接进入终审
                            content.workflow_status = 'final_review'
                            content.internal_review_status = 'pending'
                            content.content_completed = 1
                            content.review_notes = '批量驳回状态：图片和文案都已完成，进入终审'
                            logging.warning(f'DEBUG: 批量审核-图片已完成，文案审核通过，进入终审')
                        else:
                            # 图片未完成，文案审核通过，等待图片处理
                            content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                            content.internal_review_status = 'final_rej_img'  # 改为等待图片处理
                            content.content_completed = 1
                            content.review_notes = '批量驳回状态：文案审核通过，等待图片处理'
                            logging.warning(f'DEBUG: 批量审核-图片未完成，文案审核通过，等待图片处理')
                    elif content.internal_review_status in ['client_rej_text', 'client_rej_both']:
                        logging.warning(f'DEBUG: main_simple.py 批量审核检测到客户驳回状态 {content.internal_review_status}，进入客户驳回处理分支')
                        # 客户驳回状态：文案审核通过，检查图片状态
                        if content.image_completed == 1:
                            # 图片已完成，文案也审核通过，直接进入终审
                            content.workflow_status = 'final_review'
                            content.client_review_status = 'pending'  # 重置客户审核状态
                            content.internal_review_status = 'pending'
                            content.content_completed = 1
                            content.review_notes = '批量客户驳回状态：图片和文案都已完成，进入终审'
                            logging.warning(f'DEBUG: 批量审核-客户驳回-图片已完成，文案审核通过，进入终审')
                        else:
                            # 图片未完成，文案审核通过，等待图片处理
                            content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                            content.internal_review_status = 'client_rej_img'  # 改为等待图片处理
                            content.content_completed = 1
                            content.review_notes = '批量客户驳回状态：文案审核通过，等待图片处理'
                            logging.warning(f'DEBUG: 批量审核-客户驳回-图片未完成，文案审核通过，等待图片处理')
                    else:
                        logging.warning(f'DEBUG: main_simple.py 批量审核进入正常初审分支')
                        # 正常初审通过 - 检查是否已有图片
                        if content.image_urls and content.image_urls.strip() and content.image_urls != '[]':
                            # 已有图片，直接进入终审
                            content.workflow_status = 'final_review'
                            content.internal_review_status = 'pending'
                            content.review_notes = '批量初审通过，已有图片，直接进入终审'
                        else:
                            # 没有图片，需要上传图片
                            content.workflow_status = 'first_reviewed'
                            content.internal_review_status = 'first_approved'
                            content.review_notes = '批量初审通过'
                elif status == 'rejected':
                    content.workflow_status = 'draft'
                    content.review_notes = f'批量初审拒绝: {reason}'
                else:
                    continue

                content.reviewer_id = current_user.id
                content.review_time = datetime.now()
                content.updated_at = datetime.now()
                count += 1

            db.session.commit()

            return jsonify({
                'success': True,
                'message': f'批量操作成功，共处理 {count} 篇文案',
                'count': count
            })
        elif action == 'delete':
            contents = Content.query.filter(Content.id.in_(content_ids)).all()

            count = 0
            for content in contents:
                # 检查状态是否允许删除
                if content.workflow_status in ['published']:
                    continue  # 已发布的文案不能删除

                # 软删除
                content.is_deleted = True
                content.deleted_at = datetime.now()
                content.deleted_by = current_user.id
                count += 1

            db.session.commit()

            return jsonify({
                'success': True,
                'message': f'批量删除成功，共删除 {count} 篇文案',
                'count': count
            })
        else:
            return jsonify({'success': False, 'message': '不支持的操作'}), 400

    except Exception as e:
        db.session.rollback()
        print(f"Error batch action: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@main_simple_bp.route('/contents/get-marks/<int:category_id>')
@login_required
def get_marks(category_id):
    """获取模板分类的标记（带自动迁移功能）"""
    try:
        print(f"DEBUG - 简化版API被调用: 获取分类标记，分类ID: {category_id}")

        # 检查是否需要迁移marks字段
        total_templates = Template.query.filter_by(category_id=category_id, status=True).count()
        templates_with_marks = Template.query.filter_by(category_id=category_id, status=True).filter(Template.marks.isnot(None)).count()

        print(f"DEBUG - 该分类总模板数: {total_templates}, 有marks的模板数: {templates_with_marks}")

        # 如果有模板但marks字段缺失，执行迁移
        if total_templates > 0 and templates_with_marks == 0:
            print("DEBUG - 检测到marks字段完全缺失，开始自动迁移...")
            migrate_category_marks(category_id)
        elif total_templates > 0 and templates_with_marks < total_templates * 0.8:
            print("DEBUG - 检测到marks字段部分缺失，开始自动迁移...")
            migrate_category_marks(category_id)

        # 查询完整的模板对象
        templates = Template.query.filter_by(category_id=category_id, status=True).all()

        print(f"DEBUG - 查询到 {len(templates)} 个模板")

        # 收集所有标记（从marks字段读取）
        all_marks = set()
        processed_count = 0
        empty_marks_count = 0

        for template in templates:
            # 使用get_marks_list()方法，支持JSON字段和实时提取
            try:
                marks_list = template.get_marks_list()
                if marks_list:  # 只有非空列表才处理
                    all_marks.update(marks_list)
                    processed_count += 1
                    print(f"DEBUG - 模板 {template.id} 的标记: {marks_list}")
                else:
                    empty_marks_count += 1
                    print(f"DEBUG - 模板 {template.id} 没有标记")
            except Exception as e:
                print(f"WARNING - 模板 {template.id} 的标记获取失败: {e}")
                empty_marks_count += 1

        print(f"DEBUG - 处理了 {processed_count} 个有标记的模板，{empty_marks_count} 个模板marks字段为空")
        print(f"DEBUG - 提取到 {len(all_marks)} 个唯一标记: {sorted(list(all_marks))}")

        result = {
            'success': True,
            'marks': sorted(list(all_marks)),
            'template_count': len(templates),
            'processed_count': processed_count,
            'empty_marks_count': empty_marks_count
        }

        return jsonify(result)

    except Exception as e:
        print(f"ERROR - 获取模板标记失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def migrate_category_marks(category_id):
    """迁移指定分类的marks字段"""
    try:
        import re
        from sqlalchemy import text

        print(f"DEBUG - 开始迁移分类 {category_id} 的marks字段")

        # 先清空该分类的marks字段
        print(f"DEBUG - 清空分类 {category_id} 的marks字段")
        db.session.execute(
            text("UPDATE templates SET marks = NULL WHERE category_id = :category_id"),
            {"category_id": category_id}
        )
        db.session.commit()

        # 查询该分类的所有模板
        templates = Template.query.filter_by(category_id=category_id, status=True).all()

        migrated_count = 0
        for template in templates:
            # 提取标记
            marks = set()
            if template.title:
                title_marks = re.findall(r'\{([^}]+)\}', template.title)
                marks.update(title_marks)
            if template.content:
                content_marks = re.findall(r'\{([^}]+)\}', template.content)
                marks.update(content_marks)

            marks_list = sorted(list(marks))

            # 使用原生SQL更新，确保正确的UTF-8编码
            if marks_list:
                import json
                marks_json = json.dumps(marks_list, ensure_ascii=False)
                print(f"DEBUG - 迁移模板 {template.id}: {marks_list} -> JSON: {marks_json}")

                db.session.execute(
                    text("UPDATE templates SET marks = :marks WHERE id = :template_id"),
                    {"marks": marks_json, "template_id": template.id}
                )
                migrated_count += 1
            else:
                # 设置为空数组
                db.session.execute(
                    text("UPDATE templates SET marks = '[]' WHERE id = :template_id"),
                    {"template_id": template.id}
                )
                print(f"DEBUG - 模板 {template.id}: 无标记")

        db.session.commit()
        print(f"DEBUG - 分类 {category_id} 迁移完成，共迁移 {migrated_count} 个有标记的模板")

    except Exception as e:
        print(f"ERROR - 迁移分类 {category_id} 失败: {e}")
        import traceback
        traceback.print_exc()
        db.session.rollback()


@main_simple_bp.route('/contents/force-migrate-marks/<int:category_id>')
@login_required
def force_migrate_marks(category_id):
    """强制迁移指定分类的marks字段"""
    try:
        import re
        import json

        print(f"DEBUG - 强制迁移分类 {category_id} 的marks字段")

        # 查询该分类的模板
        templates = Template.query.filter_by(category_id=category_id, status=True).all()
        print(f"DEBUG - 找到 {len(templates)} 个模板")

        results = []
        success_count = 0

        for template in templates:
            try:
                # 提取标记
                marks = set()
                if template.title:
                    title_marks = re.findall(r'\{([^}]+)\}', template.title)
                    marks.update(title_marks)
                    print(f"DEBUG - 模板 {template.id} 标题标记: {title_marks}")

                if template.content:
                    content_marks = re.findall(r'\{([^}]+)\}', template.content)
                    marks.update(content_marks)
                    print(f"DEBUG - 模板 {template.id} 内容标记: {content_marks}")

                marks_list = sorted(list(marks))
                print(f"DEBUG - 模板 {template.id} 合并标记: {marks_list}")

                # 使用逗号分隔的字符串存储
                template.marks = ','.join(marks_list) if marks_list else ''

                results.append({
                    'template_id': template.id,
                    'title': template.title[:30] + '...' if len(template.title) > 30 else template.title,
                    'marks': marks_list
                })

                if marks_list:
                    success_count += 1

            except Exception as e:
                print(f"ERROR - 处理模板 {template.id} 失败: {e}")
                results.append({
                    'template_id': template.id,
                    'error': str(e)
                })

        # 提交到数据库
        db.session.commit()
        print(f"DEBUG - 迁移完成，成功处理 {success_count} 个有标记的模板")

        return jsonify({
            'success': True,
            'message': f'分类 {category_id} 迁移完成',
            'total_templates': len(templates),
            'success_count': success_count,
            'results': results[:5]  # 只返回前5个结果
        })

    except Exception as e:
        print(f"ERROR - 强制迁移失败: {e}")
        import traceback
        traceback.print_exc()
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/test-encoding')
@login_required
def test_encoding():
    """测试中文编码"""
    try:
        import json

        # 测试不同的JSON编码方式
        test_marks = ["品牌名称", "商品名称", "店铺地址"]

        # 方式1：默认编码
        json1 = json.dumps(test_marks)

        # 方式2：ensure_ascii=False
        json2 = json.dumps(test_marks, ensure_ascii=False)

        # 方式3：ensure_ascii=False + separators
        json3 = json.dumps(test_marks, ensure_ascii=False, separators=(',', ':'))

        # 测试解析
        parsed1 = json.loads(json1)
        parsed2 = json.loads(json2)
        parsed3 = json.loads(json3)

        return jsonify({
            'success': True,
            'original': test_marks,
            'encoding_tests': {
                'default': {'json': json1, 'parsed': parsed1},
                'ensure_ascii_false': {'json': json2, 'parsed': parsed2},
                'with_separators': {'json': json3, 'parsed': parsed3}
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/contents/clear-marks')
@login_required
def clear_marks():
    """清空所有marks字段"""
    try:
        from sqlalchemy import text

        result = db.session.execute(text("UPDATE templates SET marks = NULL"))
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已清空 {result.rowcount} 个模板的marks字段'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/contents/fix-marks-column')
@login_required
def fix_marks_column():
    """修改marks字段类型从JSON改为TEXT"""
    try:
        from sqlalchemy import text

        print("开始修改marks字段类型...")

        # 1. 清空marks字段
        result1 = db.session.execute(text("UPDATE templates SET marks = NULL"))
        print(f"清空了 {result1.rowcount} 个模板的marks字段")

        # 2. 修改字段类型
        db.session.execute(text("ALTER TABLE templates MODIFY COLUMN marks TEXT COMMENT '模板中的标记列表，逗号分隔存储'"))
        print("marks字段类型已修改为TEXT")

        # 3. 提交更改
        db.session.commit()
        print("数据库修改完成")

        return jsonify({
            'success': True,
            'message': 'marks字段类型已成功修改为TEXT，现在可以使用逗号分隔的字符串存储标记'
        })

    except Exception as e:
        print(f"修改marks字段类型失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/force-fix-marks-column')
@login_required
def force_fix_marks_column():
    """强制修改marks字段类型"""
    try:
        from sqlalchemy import text

        print("开始强制修改marks字段类型...")

        # 1. 删除marks字段
        try:
            db.session.execute(text("ALTER TABLE templates DROP COLUMN marks"))
            print("已删除marks字段")
        except Exception as e:
            print(f"删除marks字段失败（可能不存在）: {e}")

        # 2. 重新添加marks字段为TEXT类型
        db.session.execute(text("ALTER TABLE templates ADD COLUMN marks TEXT COMMENT '模板中的标记列表，逗号分隔存储'"))
        print("已重新添加marks字段为TEXT类型")

        # 3. 提交更改
        db.session.commit()
        print("数据库修改完成")

        return jsonify({
            'success': True,
            'message': 'marks字段已强制修改为TEXT类型'
        })

    except Exception as e:
        print(f"强制修改marks字段类型失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/fix-marks-column-final')
@login_required
def fix_marks_column_final():
    """最终修复marks字段类型问题"""
    try:
        from sqlalchemy import text
        import pymysql

        print("开始最终修复marks字段类型...")

        # 方法1：直接使用pymysql连接执行SQL
        try:
            # 获取数据库连接信息
            from app import db
            engine = db.engine

            # 使用原生连接执行SQL
            with engine.raw_connection() as conn:
                cursor = conn.cursor()

                # 1. 清空marks字段
                cursor.execute("UPDATE templates SET marks = NULL")
                print("已清空marks字段")

                # 2. 修改字段类型
                cursor.execute("ALTER TABLE templates MODIFY COLUMN marks TEXT COMMENT '模板中的标记列表，逗号分隔存储'")
                print("已修改marks字段类型为TEXT")

                # 3. 提交更改
                conn.commit()
                print("数据库修改已提交")

        except Exception as e:
            print(f"方法1失败: {e}")

            # 方法2：使用SQLAlchemy的text执行
            try:
                db.session.execute(text("SET foreign_key_checks = 0"))
                db.session.execute(text("ALTER TABLE templates MODIFY COLUMN marks TEXT"))
                db.session.execute(text("SET foreign_key_checks = 1"))
                db.session.commit()
                print("方法2成功：已修改marks字段类型")
            except Exception as e2:
                print(f"方法2也失败: {e2}")
                db.session.rollback()
                raise e2

        return jsonify({
            'success': True,
            'message': 'marks字段类型已成功修改为TEXT，现在可以正常保存模板了'
        })

    except Exception as e:
        print(f"最终修复marks字段类型失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/test-marks-encoding')
@login_required
def test_marks_encoding():
    """测试编码问题"""
    try:
        import json

        # 测试数据
        test_marks = ['品牌名称', '店铺地址', '标记2']

        # 不同的编码方式
        json1 = json.dumps(test_marks, ensure_ascii=False)
        json2 = json.dumps(test_marks, ensure_ascii=True)

        # 解析测试
        parsed1 = json.loads(json1)
        parsed2 = json.loads(json2)

        return jsonify({
            'success': True,
            'original': test_marks,
            'json_ensure_ascii_false': json1,
            'json_ensure_ascii_true': json2,
            'parsed_from_false': parsed1,
            'parsed_from_true': parsed2,
            'encoding_test': {
                'utf8_bytes': json1.encode('utf-8').decode('utf-8'),
                'ascii_unicode': json2
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/force-fix-marks-field')
@login_required
def force_fix_marks_field():
    """强制修复marks字段类型为TEXT"""
    try:
        import pymysql
        from app import db

        # 获取数据库连接配置
        engine = db.engine

        # 使用原生pymysql连接
        connection_info = engine.url

        # 创建原生连接
        connection = pymysql.connect(
            host=connection_info.host,
            port=connection_info.port or 3306,
            user=connection_info.username,
            password=connection_info.password,
            database=connection_info.database,
            charset='utf8mb4'
        )

        try:
            with connection.cursor() as cursor:
                # 1. 清空marks字段
                cursor.execute("UPDATE templates SET marks = NULL")
                print("已清空marks字段")

                # 2. 修改字段类型为TEXT
                cursor.execute("ALTER TABLE templates MODIFY COLUMN marks TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模板标记，逗号分隔'")
                print("已修改marks字段类型为TEXT")

                # 3. 提交更改
                connection.commit()
                print("数据库修改已提交")

            return jsonify({
                'success': True,
                'message': 'marks字段已成功修改为TEXT类型，现在可以正常保存中文了'
            })

        finally:
            connection.close()

    except Exception as e:
        print(f"强制修复marks字段失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/ultimate-fix-marks')
@login_required
def ultimate_fix_marks():
    """终极修复marks字段 - 使用多种方法"""
    try:
        from sqlalchemy import text
        import subprocess
        import os

        results = []

        # 方法1: 直接使用SQLAlchemy执行原生SQL
        try:
            # 禁用外键检查
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 0"))

            # 清空marks字段
            result1 = db.session.execute(text("UPDATE templates SET marks = NULL"))
            results.append(f"清空了 {result1.rowcount} 个模板的marks字段")

            # 修改字段类型
            db.session.execute(text("ALTER TABLE templates MODIFY COLUMN marks TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"))
            results.append("成功修改marks字段类型为TEXT")

            # 重新启用外键检查
            db.session.execute(text("SET FOREIGN_KEY_CHECKS = 1"))

            # 提交更改
            db.session.commit()
            results.append("数据库更改已提交")

            return jsonify({
                'success': True,
                'message': 'marks字段已成功修改为TEXT类型',
                'details': results
            })

        except Exception as e:
            db.session.rollback()
            results.append(f"SQLAlchemy方法失败: {e}")

            # 方法2: 如果SQLAlchemy失败，返回手动SQL命令
            manual_sql = """
-- 请在MySQL命令行中手动执行以下SQL命令：

USE your_database_name;

-- 1. 清空marks字段
UPDATE templates SET marks = NULL;

-- 2. 修改字段类型
ALTER TABLE templates MODIFY COLUMN marks TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模板标记，逗号分隔';

-- 3. 验证修改
DESCRIBE templates;
"""

            return jsonify({
                'success': False,
                'error': str(e),
                'manual_sql': manual_sql,
                'details': results
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/check-db-field-type')
@login_required
def check_db_field_type():
    """检查数据库字段类型"""
    try:
        from sqlalchemy import text

        # 查询字段信息
        result = db.session.execute(text("DESCRIBE templates"))
        fields = result.fetchall()

        # 找到marks字段
        marks_field = None
        for field in fields:
            if field[0] == 'marks':  # 字段名
                marks_field = {
                    'field': field[0],
                    'type': field[1],
                    'null': field[2],
                    'key': field[3],
                    'default': field[4],
                    'extra': field[5]
                }
                break

        return jsonify({
            'success': True,
            'marks_field': marks_field,
            'all_fields': [{'field': f[0], 'type': f[1]} for f in fields]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/force-change-marks-field')
@login_required
def force_change_marks_field():
    """强制修改marks字段类型"""
    try:
        from sqlalchemy import text
        import time

        results = []

        # 步骤1: 检查当前字段类型
        result = db.session.execute(text("DESCRIBE templates"))
        fields = result.fetchall()
        for field in fields:
            if field[0] == 'marks':
                results.append(f"当前marks字段类型: {field[1]}")
                break

        # 步骤2: 备份数据并清空marks字段
        db.session.execute(text("UPDATE templates SET marks = NULL"))
        results.append("已清空marks字段")

        # 步骤3: 删除字段
        try:
            db.session.execute(text("ALTER TABLE templates DROP COLUMN marks"))
            results.append("已删除marks字段")
        except Exception as e:
            results.append(f"删除字段失败: {e}")

        # 步骤4: 重新添加字段为TEXT类型
        try:
            db.session.execute(text("ALTER TABLE templates ADD COLUMN marks TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模板标记，逗号分隔'"))
            results.append("已重新添加marks字段为TEXT类型")
        except Exception as e:
            results.append(f"添加字段失败: {e}")

        # 步骤5: 提交更改
        db.session.commit()
        results.append("数据库更改已提交")

        # 步骤6: 验证修改结果
        result = db.session.execute(text("DESCRIBE templates"))
        fields = result.fetchall()
        for field in fields:
            if field[0] == 'marks':
                results.append(f"修改后marks字段类型: {field[1]}")
                break

        return jsonify({
            'success': True,
            'message': 'marks字段已强制修改为TEXT类型',
            'steps': results
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e),
            'steps': results
        }), 500


@main_simple_bp.route('/contents/check-field-charset')
@login_required
def check_field_charset():
    """检查字段字符集设置"""
    try:
        from sqlalchemy import text

        # 查询字段的详细信息，包括字符集
        result = db.session.execute(text("""
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                CHARACTER_SET_NAME,
                COLLATION_NAME,
                COLUMN_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'templates'
            AND COLUMN_NAME IN ('content', 'marks', 'title')
            ORDER BY COLUMN_NAME
        """))

        fields = result.fetchall()
        field_info = []
        for field in fields:
            field_info.append({
                'column_name': field[0],
                'data_type': field[1],
                'character_set': field[2],
                'collation': field[3],
                'column_type': field[4]
            })

        return jsonify({
            'success': True,
            'fields': field_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/fix-marks-charset')
@login_required
def fix_marks_charset():
    """修复marks字段字符集，使其与content字段一致"""
    try:
        from sqlalchemy import text

        results = []

        # 1. 查看当前字段设置
        result = db.session.execute(text("""
            SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_SET_NAME, COLLATION_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'templates'
            AND COLUMN_NAME IN ('content', 'marks')
        """))

        current_settings = {}
        for row in result.fetchall():
            current_settings[row[0]] = {
                'data_type': row[1],
                'charset': row[2],
                'collation': row[3]
            }

        results.append(f"当前设置: {current_settings}")

        # 2. 获取content字段的字符集设置
        content_charset = current_settings.get('content', {}).get('charset', 'utf8mb4')
        content_collation = current_settings.get('content', {}).get('collation', 'utf8mb4_unicode_ci')

        # 3. 清空marks字段
        db.session.execute(text("UPDATE templates SET marks = NULL"))
        results.append("已清空marks字段")

        # 4. 修改marks字段为与content相同的字符集
        alter_sql = f"""
            ALTER TABLE templates
            MODIFY COLUMN marks JSON
            CHARACTER SET {content_charset}
            COLLATE {content_collation}
        """

        db.session.execute(text(alter_sql))
        results.append(f"已修改marks字段字符集为: {content_charset}/{content_collation}")

        # 5. 提交更改
        db.session.commit()
        results.append("数据库更改已提交")

        return jsonify({
            'success': True,
            'message': 'marks字段字符集已修改为与content字段一致',
            'details': results
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e),
            'details': results if 'results' in locals() else []
        }), 500


@main_simple_bp.route('/contents/direct-fix-marks')
@login_required
def direct_fix_marks():
    """直接修复marks字段，使其与content字段完全一致"""
    try:
        from sqlalchemy import text

        results = []

        # 1. 备份并清空marks字段
        db.session.execute(text("UPDATE templates SET marks = NULL"))
        results.append("已清空marks字段")

        # 2. 删除marks字段
        try:
            db.session.execute(text("ALTER TABLE templates DROP COLUMN marks"))
            results.append("已删除marks字段")
        except Exception as e:
            results.append(f"删除字段失败: {e}")

        # 3. 重新创建marks字段，使用与content完全相同的设置
        try:
            # 先查看content字段的定义
            result = db.session.execute(text("SHOW CREATE TABLE templates"))
            table_def = result.fetchone()[1]
            results.append(f"表定义: {table_def}")

            # 重新添加marks字段为TEXT类型，与content字段相同的字符集
            db.session.execute(text("""
                ALTER TABLE templates
                ADD COLUMN marks TEXT
                CHARACTER SET utf8mb4
                COLLATE utf8mb4_unicode_ci
                COMMENT '模板标记，逗号分隔'
            """))
            results.append("已重新创建marks字段为TEXT类型")
        except Exception as e:
            results.append(f"创建字段失败: {e}")

        # 4. 提交更改
        db.session.commit()
        results.append("数据库更改已提交")

        return jsonify({
            'success': True,
            'message': 'marks字段已重新创建为TEXT类型，与content字段一致',
            'details': results
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e),
            'details': results if 'results' in locals() else []
        }), 500


@main_simple_bp.route('/contents/test-encoding-comparison')
@login_required
def test_encoding_comparison():
    """测试content和marks字段的编码对比"""
    try:
        from sqlalchemy import text

        # 获取最新的模板
        template = Template.query.order_by(Template.id.desc()).first()
        if not template:
            return jsonify({'success': False, 'error': '没有找到模板'})

        # 测试数据
        test_content = "测试内容：{品牌名称}{商品名称}"
        test_marks_json = '["测试标记", "品牌名称", "商品名称"]'

        # 1. 测试content字段更新
        db.session.execute(
            text("UPDATE templates SET content = :content WHERE id = :id"),
            {"content": test_content, "id": template.id}
        )

        # 2. 测试marks字段更新
        db.session.execute(
            text("UPDATE templates SET marks = :marks WHERE id = :id"),
            {"marks": test_marks_json, "id": template.id}
        )

        db.session.commit()

        # 3. 读取并对比
        result = db.session.execute(
            text("SELECT content, marks FROM templates WHERE id = :id"),
            {"id": template.id}
        ).fetchone()

        return jsonify({
            'success': True,
            'template_id': template.id,
            'test_data': {
                'input_content': test_content,
                'input_marks': test_marks_json,
                'output_content': result[0],
                'output_marks': result[1]
            },
            'comparison': {
                'content_correct': result[0] == test_content,
                'marks_correct': result[1] == test_marks_json
            }
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/final-fix-database')
@login_required
def final_fix_database():
    """最终修复数据库字符集问题"""
    try:
        import pymysql
        from app import db

        results = []

        # 获取数据库连接信息
        engine = db.engine
        connection_info = engine.url

        # 创建原生pymysql连接
        connection = pymysql.connect(
            host=connection_info.host,
            port=connection_info.port or 3306,
            user=connection_info.username,
            password=connection_info.password,
            database=connection_info.database,
            charset='utf8mb4'
        )

        try:
            with connection.cursor() as cursor:
                # 1. 查看当前表和字段的字符集
                cursor.execute("""
                    SELECT
                        COLUMN_NAME,
                        CHARACTER_SET_NAME,
                        COLLATION_NAME,
                        DATA_TYPE
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_NAME = 'templates'
                    AND COLUMN_NAME IN ('content', 'marks', 'title')
                """, (connection_info.database,))

                current_info = cursor.fetchall()
                results.append(f"当前字段信息: {current_info}")

                # 2. 备份并清空marks字段
                cursor.execute("UPDATE templates SET marks = NULL")
                results.append("已清空marks字段")

                # 3. 修改marks字段字符集为utf8mb4
                cursor.execute("""
                    ALTER TABLE templates
                    MODIFY COLUMN marks JSON
                    CHARACTER SET utf8mb4
                    COLLATE utf8mb4_unicode_ci
                """)
                results.append("已修改marks字段字符集为utf8mb4")

                # 4. 验证修改结果
                cursor.execute("""
                    SELECT
                        COLUMN_NAME,
                        CHARACTER_SET_NAME,
                        COLLATION_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_NAME = 'templates'
                    AND COLUMN_NAME = 'marks'
                """, (connection_info.database,))

                new_info = cursor.fetchone()
                results.append(f"修改后marks字段: {new_info}")

                # 5. 提交更改
                connection.commit()
                results.append("数据库更改已提交")

            return jsonify({
                'success': True,
                'message': 'marks字段字符集已修复为utf8mb4',
                'details': results
            })

        finally:
            connection.close()

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'details': results if 'results' in locals() else []
        }), 500


@main_simple_bp.route('/contents/test-template-save')
@login_required
def test_template_save():
    """测试模板保存功能"""
    try:
        from app.models.template import Template
        from app.models.template_category import TemplateCategory

        # 创建一个测试模板对象（不保存到数据库）
        category = TemplateCategory.query.first()
        if not category:
            return jsonify({
                'success': False,
                'error': '没有找到模板分类'
            })

        test_template = Template(
            category_id=category.id,
            title='测试模板 {品牌名称}',
            content='这是 {商品名称} 的测试内容',
            creator_id=1,
            status=1
        )

        # 测试update_marks方法
        marks_list = test_template.update_marks()

        return jsonify({
            'success': True,
            'test_results': {
                'title': test_template.title,
                'content': test_template.content,
                'extracted_marks': marks_list,
                'marks_field': test_template.marks,
                'marks_field_type': type(test_template.marks).__name__
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/verify-marks-reading')
@login_required
def verify_marks_reading():
    """验证marks字段的读取是否正常"""
    try:
        # 获取最新的几个模板
        templates = Template.query.order_by(Template.id.desc()).limit(3).all()

        result = []
        for template in templates:
            marks_list = template.get_marks_list()
            result.append({
                'id': template.id,
                'title': template.title,
                'marks_raw': template.marks,
                'marks_parsed': marks_list,
                'marks_count': len(marks_list) if marks_list else 0
            })

        return jsonify({
            'success': True,
            'message': '标记读取验证完成',
            'templates': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/debug-template-content')
@login_required
def debug_template_content():
    """调试模板内容，查看数据库中实际存储的格式"""
    try:
        templates = Template.query.limit(5).all()

        result = []
        for template in templates:
            result.append({
                'id': template.id,
                'title': template.title,
                'content': template.content[:100] + '...' if len(template.content) > 100 else template.content,
                'marks': template.marks
            })

        return jsonify({
            'success': True,
            'templates': result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/fix-template-marks-format')
@login_required
def fix_template_marks_format():
    """修复模板标题和内容中的标记格式，将《》改为{}"""
    try:
        from sqlalchemy import text
        import re

        # 获取所有模板
        templates = Template.query.all()
        updated_count = 0

        for template in templates:
            title_updated = False
            content_updated = False

            # 处理标题中的标记格式
            if template.title and ('《' in template.title and '》' in template.title):
                new_title = re.sub(r'《([^》]+)》', r'{\1}', template.title)
                if new_title != template.title:
                    print(f"更新模板 {template.id} 标题: {template.title} -> {new_title}")
                    template.title = new_title
                    title_updated = True

            # 处理内容中的标记格式
            if template.content and ('《' in template.content and '》' in template.content):
                new_content = re.sub(r'《([^》]+)》', r'{\1}', template.content)
                if new_content != template.content:
                    print(f"更新模板 {template.id} 内容中的标记格式")
                    template.content = new_content
                    content_updated = True

            if title_updated or content_updated:
                # 重新提取标记
                template.update_marks()
                updated_count += 1

        # 提交更改
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已更新 {updated_count} 个模板的标记格式，从《》改为{{}}'
        })

    except Exception as e:
        print(f"修复模板标记格式失败: {e}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/check-marks-column')
@login_required
def check_marks_column():
    """检查marks字段的数据库结构"""
    try:
        from sqlalchemy import text

        # 查询字段信息
        result = db.session.execute(text("DESCRIBE templates")).fetchall()

        marks_field_info = None
        for row in result:
            if row[0] == 'marks':  # 字段名
                marks_field_info = {
                    'field': row[0],
                    'type': row[1],
                    'null': row[2],
                    'key': row[3],
                    'default': row[4],
                    'extra': row[5]
                }
                break

        return jsonify({
            'success': True,
            'marks_field_info': marks_field_info,
            'all_fields': [{'field': row[0], 'type': row[1]} for row in result]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/contents/debug-encoding/<int:template_id>')
@login_required
def debug_encoding(template_id):
    """调试编码问题"""
    try:
        template = Template.query.get(template_id)
        if not template:
            return jsonify({'success': False, 'error': '模板不存在'})

        import re
        import json

        # 提取标记
        marks = set()
        if template.title:
            title_marks = re.findall(r'\{([^}]+)\}', template.title)
            marks.update(title_marks)
        if template.content:
            content_marks = re.findall(r'\{([^}]+)\}', template.content)
            marks.update(content_marks)

        marks_list = sorted(list(marks))

        # 测试不同的编码方式
        json1 = json.dumps(marks_list)
        json2 = json.dumps(marks_list, ensure_ascii=False)
        json3 = json.dumps(marks_list, ensure_ascii=False, separators=(',', ':'))

        # 直接使用逗号分隔的字符串
        comma_separated = ','.join(marks_list)

        return jsonify({
            'success': True,
            'template_id': template_id,
            'title': template.title,
            'content': template.content[:100] + '...' if len(template.content) > 100 else template.content,
            'extracted_marks': marks_list,
            'current_marks_db': template.marks,
            'encoding_tests': {
                'json_default': json1,
                'json_ensure_ascii_false': json2,
                'json_with_separators': json3,
                'comma_separated': comma_separated
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/contents/test-migrate/<int:template_id>')
@login_required
def test_migrate_single(template_id):
    """测试单个模板的标记提取"""
    try:
        import re
        import json

        template = Template.query.get(template_id)
        if not template:
            return jsonify({'success': False, 'error': '模板不存在'})

        print(f"DEBUG - 测试模板 {template_id}")
        print(f"DEBUG - 标题: {template.title}")
        print(f"DEBUG - 内容: {template.content[:100]}...")

        # 提取标记
        marks = set()
        if template.title:
            title_marks = re.findall(r'\{([^}]+)\}', template.title)
            marks.update(title_marks)
            print(f"DEBUG - 标题标记: {title_marks}")

        if template.content:
            content_marks = re.findall(r'\{([^}]+)\}', template.content)
            marks.update(content_marks)
            print(f"DEBUG - 内容标记: {content_marks}")

        marks_list = sorted(list(marks))
        print(f"DEBUG - 合并标记: {marks_list}")

        # 更新marks字段
        marks_json = json.dumps(marks_list, ensure_ascii=False)
        template.marks = marks_json
        db.session.commit()

        print(f"DEBUG - 保存的JSON: {marks_json}")

        # 重新查询验证
        template_fresh = Template.query.get(template_id)
        print(f"DEBUG - 数据库中的marks: {template_fresh.marks}")

        return jsonify({
            'success': True,
            'template_id': template_id,
            'title': template.title,
            'extracted_marks': marks_list,
            'saved_marks_json': marks_json,
            'db_marks': template_fresh.marks
        })

    except Exception as e:
        print(f"ERROR - 测试迁移失败: {e}")
        import traceback
        traceback.print_exc()
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/contents/get-batch-count/<int:task_id>')
@login_required
def get_task_batch_count(task_id):
    """获取任务下的批次数量"""
    try:
        # 查询该任务下的批次数量
        batch_count = Batch.query.filter_by(task_id=task_id).count()

        return jsonify({
            'success': True,
            'batch_count': batch_count,
            'next_batch_number': batch_count + 1
        })

    except Exception as e:
        print(f"获取任务批次数量失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/validate-generation-count', methods=['POST'])
@login_required
def validate_generation_count():
    """验证生成数量和重复性控制"""
    try:
        print("=== 开始验证生成数量 ===")

        # 检查请求内容类型
        print(f"Content-Type: {request.content_type}")
        print(f"Request method: {request.method}")

        # 导入必要的模型
        from app.models.content import Content
        from app.models.template import Template, TemplateCategory
        from app.models.task import Task

        # 获取请求数据
        data = request.get_json()
        print(f"原始请求数据: {data}")

        if not data:
            print("错误: 无法获取JSON数据")
            return jsonify({
                'success': False,
                'error': '无效的请求数据'
            }), 400

        template_category_id = data.get('template_category_id')
        client_id = data.get('client_id')
        task_id = data.get('task_id')
        task_name = data.get('task_name')
        duplicate_control = data.get('duplicate_control', 'task')
        requested_count = data.get('requested_count', 0)

        print(f"解析后的参数: category_id={template_category_id}, client_id={client_id}, task_id={task_id}, task_name={task_name}, duplicate_control={duplicate_control}, requested_count={requested_count}")

        if not template_category_id:
            print("错误: 未选择模板分类")
            return jsonify({
                'success': False,
                'error': '请选择模板分类'
            })

        # 转换为整数
        try:
            template_category_id = int(template_category_id)
            if client_id:
                client_id = int(client_id)
            if task_id and task_id != '0':
                task_id = int(task_id)
            else:
                task_id = None
            print(f"转换后的参数: category_id={template_category_id}, client_id={client_id}, task_id={task_id}")
        except (ValueError, TypeError) as e:
            print(f"参数转换错误: {e}")
            return jsonify({
                'success': False,
                'error': f'参数格式错误: {str(e)}'
            }), 400

        # 获取模板分类信息
        print(f"查询模板分类: {template_category_id}")
        category = TemplateCategory.query.get(template_category_id)
        if not category:
            print(f"模板分类不存在: {template_category_id}")
            return jsonify({
                'success': False,
                'error': '模板分类不存在'
            })
        print(f"找到模板分类: {category.name}")

        # 获取该分类下的所有模板
        print(f"查询分类下的模板...")
        templates = Template.query.filter_by(
            category_id=template_category_id,
            status=True
        ).all()
        total_templates = len(templates)
        print(f"找到 {total_templates} 个模板")

        # 获取模板中的标记
        all_marks = set()
        for template in templates:
            if template.marks:
                all_marks.update(template.marks)

        # 过滤掉固定标记
        fixed_marks = {'话题', '定位', '@用户', 'at_users', 'location', 'topics'}
        user_marks = [mark for mark in all_marks if mark not in fixed_marks]

        print(f"找到 {len(all_marks)} 个标记，用户需要填写 {len(user_marks)} 个标记")
        print(f"用户标记: {user_marks}")

        # 根据重复性控制计算可用模板数量
        usable_templates = total_templates
        duplicate_info = ""

        print(f"重复性控制: {duplicate_control}")

        if duplicate_control == 'client' and client_id:
            print(f"客户不重复模式，客户ID: {client_id}")
            # 客户不重复：查询该客户已使用的模板
            if templates:
                template_ids = [t.id for t in templates]
                print(f"模板IDs: {template_ids}")
                used_template_ids = db.session.query(Content.template_id).filter(
                    Content.client_id == client_id,
                    Content.template_id.in_(template_ids),
                    Content.is_deleted != True  # 排除已删除的文案
                ).distinct().all()
                used_template_ids = [t[0] for t in used_template_ids if t[0] is not None]
                usable_templates = total_templates - len(used_template_ids)
                duplicate_info = f"该客户已使用 {len(used_template_ids)} 个模板"
                print(f"客户已使用模板: {used_template_ids}")
            else:
                duplicate_info = "该客户暂无已使用模板"

        elif duplicate_control == 'task':
            if task_id:
                print(f"任务不重复模式，任务ID: {task_id}")
                # 任务不重复：查询该任务已使用的模板
                if templates:
                    template_ids = [t.id for t in templates]
                    print(f"模板IDs: {template_ids}")
                    used_template_ids = db.session.query(Content.template_id).filter(
                        Content.task_id == task_id,
                        Content.template_id.in_(template_ids),
                        Content.is_deleted != True  # 排除已删除的文案
                    ).distinct().all()
                    used_template_ids = [t[0] for t in used_template_ids if t[0] is not None]
                    usable_templates = total_templates - len(used_template_ids)
                    duplicate_info = f"该任务已使用 {len(used_template_ids)} 个模板"
                    print(f"任务已使用模板: {used_template_ids}")
                else:
                    duplicate_info = "该任务暂无已使用模板"
            else:
                # task_id=0，检查是否有同名任务存在
                print(f"任务不重复模式，task_id=0，检查同名任务")
                if client_id:
                    # 使用前端传递的任务名称，如果没有则使用默认名称
                    if task_name:
                        check_task_name = task_name.strip()
                        print(f"使用前端传递的任务名称: {check_task_name}")
                    else:
                        # 获取默认任务名称（与生成文案时的逻辑一致）
                        from datetime import datetime
                        now = datetime.now()
                        check_task_name = f'{now.year}年{now.month:02d}月{now.day:02d}日任务'
                        print(f"使用默认任务名称: {check_task_name}")

                    print(f"检查同名任务: {check_task_name}")

                    # 查找同名任务
                    existing_task = Task.query.filter_by(
                        client_id=client_id,
                        name=check_task_name
                    ).first()

                    if existing_task:
                        print(f"找到同名任务: id={existing_task.id}, name={existing_task.name}")
                        # 检查该任务已使用的模板
                        if templates:
                            template_ids = [t.id for t in templates]
                            print(f"模板IDs: {template_ids}")
                            used_template_ids = db.session.query(Content.template_id).filter(
                                Content.task_id == existing_task.id,
                                Content.template_id.in_(template_ids),
                                Content.is_deleted != True  # 排除已删除的文案
                            ).distinct().all()
                            used_template_ids = [t[0] for t in used_template_ids if t[0] is not None]
                            usable_templates = total_templates - len(used_template_ids)
                            duplicate_info = f"该任务已使用 {len(used_template_ids)} 个模板"
                            print(f"同名任务已使用模板: {used_template_ids}")
                        else:
                            duplicate_info = "该任务暂无已使用模板"
                    else:
                        print("未找到同名任务，新任务")
                        duplicate_info = "新任务，所有模板可用"
                else:
                    duplicate_info = "新任务，所有模板可用"
        else:
            # 批次不重复等其他模式
            duplicate_info = "新批次，所有模板可用"
            print(f"无重复限制: {duplicate_info}")

        # 计算可生成数量
        available_count = max(0, usable_templates)
        print(f"可用模板数: {usable_templates}, 可生成数量: {available_count}")

        result = {
            'success': True,
            'category_name': category.name,
            'total_templates': total_templates,
            'usable_templates': usable_templates,
            'available_count': available_count,
            'marks_count': len(user_marks),  # 只统计用户需要填写的标记
            'user_marks': user_marks,  # 返回用户标记列表
            'duplicate_info': duplicate_info,
            'requested_count': requested_count
        }

        print(f"返回结果: {result}")
        print("=== 验证生成数量完成 ===")

        return jsonify(result)

    except Exception as e:
        print(f"验证生成数量失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500


@main_simple_bp.route('/contents/check-task-name', methods=['POST'])
@login_required
def check_task_name():
    """检查任务名称是否已存在"""
    try:
        data = request.get_json()
        client_id = data.get('client_id')
        task_name = data.get('task_name')

        if not client_id or not task_name:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        # 查询是否存在同名任务
        existing_task = Task.query.filter_by(
            client_id=int(client_id),
            name=task_name
        ).first()

        if existing_task:
            # 任务已存在，查询批次数量
            batch_count = Batch.query.filter_by(task_id=existing_task.id).count()

            return jsonify({
                'success': True,
                'task_exists': True,
                'task_id': existing_task.id,
                'batch_count': batch_count,
                'next_batch_number': batch_count + 1
            })
        else:
            # 任务不存在
            return jsonify({
                'success': True,
                'task_exists': False,
                'batch_count': 0,
                'next_batch_number': 1
            })

    except Exception as e:
        print(f"检查任务名称失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500





# === 新功能模块路由 ===

@main_simple_bp.route('/image-upload')
@login_required
@menu_permission_required('/simple/image-upload')
def image_upload():
    """图片上传客户列表页面"""
    from app.models.client import Client
    from app.models.content import Content
    from sqlalchemy import func

    # 获取有待上传图片的客户列表
    # 查询条件：初审通过需要上传图片的文章
    clients_with_pending = db.session.query(
        Client.id,
        Client.name,
        func.count(Content.id).label('pending_count')
    ).join(Content, Client.id == Content.client_id).filter(
        Content.is_deleted == False,
        db.or_(
            # 初审通过，需要上传图片（无论image_completed状态）
            Content.workflow_status == 'first_reviewed',
            # 图片已上传但可能需要重新上传
            Content.workflow_status == 'image_uploaded',
            # 终审驳回，需要重新处理图片
            Content.internal_review_status.like('final_rej%')
        )
    ).group_by(Client.id, Client.name).order_by(Client.name).all()

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回页面内容
        return render_template('image/upload_clients.html',
                             clients_with_pending=clients_with_pending)
    else:
        # 非AJAX请求，返回完整页面布局
        return render_template('main_simple.html',
                             page_content=render_template('image/upload_clients.html',
                                                        clients_with_pending=clients_with_pending),
                             current_page='image-upload')


@main_simple_bp.route('/image-upload/client/<int:client_id>')
@login_required
@menu_permission_required('/simple/image-upload')
def image_upload_client_detail(client_id):
    """客户图片上传详情页面"""
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回页面内容
        from app.models.content import Content
        from app.models.image import ContentImage
        from app.models.client import Client
        from app.models.task import Task

        # 获取客户信息
        client = Client.query.get_or_404(client_id)

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 获取筛选参数
        task_id = request.args.get('task_id', type=int)
        status = request.args.get('status')

        # 构建查询条件，使用joinedload预加载关联对象
        from sqlalchemy.orm import joinedload

        query = Content.query.options(
            joinedload(Content.client),
            joinedload(Content.task)
        ).filter(
            Content.client_id == client_id,  # 只查询当前客户的内容
            db.or_(
                # 初审通过，需要上传图片
                Content.workflow_status == 'first_reviewed',
                # 图片已上传但可能需要重新上传
                Content.workflow_status == 'image_uploaded',
                # 终审驳回，需要重新处理图片
                Content.internal_review_status.like('final_rej%')
            ),
            Content.is_deleted == False
        )

        # 应用筛选条件
        if task_id:
            query = query.filter(Content.task_id == task_id)

        if status:
            if status == 'first_reviewed':
                query = query.filter(Content.workflow_status == 'first_reviewed')
            elif status == 'image_uploaded':
                query = query.filter(Content.workflow_status == 'image_uploaded')
            elif status == 'final_rejected':
                query = query.filter(Content.internal_review_status.like('final_rej%'))

        # 按创建时间倒序排列
        query = query.order_by(Content.created_at.desc())

        # 分页
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        content_data = []
        for content in pagination.items:
            # 获取图片信息
            images = ContentImage.get_by_content(content.id)
            image_count = len(images)

            # 解析图片URL
            image_urls = []
            if content.image_urls:
                try:
                    import json
                    image_urls = json.loads(content.image_urls)
                except:
                    image_urls = []

            total_images = image_count + len(image_urls)

            content_data.append({
                'id': content.id,
                'title': content.title,
                'content': content.content,
                'client_name': content.client.name if content.client else '未知客户',
                'task_name': content.task.name if content.task else '未知任务',
                'workflow_status': content.workflow_status,
                'internal_review_status': content.internal_review_status,
                'created_at': content.created_at.strftime('%Y-%m-%d %H:%M'),
                'image_count': total_images,
                'image_completed': content.image_completed
            })

        # 获取当前客户的任务列表
        client_tasks = Task.query.filter_by(client_id=client_id).all()

        # 计算状态统计
        status_counts = {
            'first_reviewed': Content.query.filter(
                Content.client_id == client_id,
                Content.workflow_status == 'first_reviewed',
                Content.is_deleted == False
            ).count(),
            'image_uploaded': Content.query.filter(
                Content.client_id == client_id,
                Content.workflow_status == 'image_uploaded',
                Content.is_deleted == False
            ).count(),
            'final_rejected': Content.query.filter(
                Content.client_id == client_id,
                Content.internal_review_status.like('final_rej%'),
                Content.is_deleted == False
            ).count()
        }








        return render_template('image/upload_detail.html',
                             content_data=content_data,
                             client=client,
                             tasks=client_tasks,
                             pagination=pagination,
                             per_page=per_page,
                             status_counts=status_counts)
    else:
        # 非AJAX请求，返回完整页面布局
        from app.models.content import Content
        from app.models.client import Client
        from app.models.task import Task

        # 获取客户信息
        client = Client.query.get_or_404(client_id)

        # 获取当前客户的任务列表
        client_tasks = Task.query.filter_by(client_id=client_id).all()

        # 计算状态统计
        status_counts = {
            'first_reviewed': Content.query.filter(
                Content.client_id == client_id,
                Content.workflow_status == 'first_reviewed',
                Content.is_deleted == False
            ).count(),
            'image_uploaded': Content.query.filter(
                Content.client_id == client_id,
                Content.workflow_status == 'image_uploaded',
                Content.is_deleted == False
            ).count(),
            'final_rejected': Content.query.filter(
                Content.client_id == client_id,
                Content.internal_review_status.like('final_rej%'),
                Content.is_deleted == False
            ).count()
        }

        return render_template('main_simple.html',
                             page_content=render_template('image/upload_detail.html',
                                                        content_data=[],
                                                        client=client,
                                                        tasks=client_tasks,
                                                        pagination=None,
                                                        per_page=20,
                                                        status_counts=status_counts),
                             current_page='image-upload')









        return render_template('main_simple.html',
                             page_content=render_template('image/upload.html',
                                                        content_data=content_data,
                                                        clients=clients_with_content,
                                                        tasks=[],
                                                        pagination=pagination,
                                                        per_page=per_page,
                                                        status_counts=status_counts),
                             current_page='image-upload')


@main_simple_bp.route('/api/get-client-tasks/<int:client_id>')
def get_client_tasks(client_id):
    """获取指定客户的任务列表（只包含有待上传图片文案的任务）"""
    try:
        print(f"=== 获取客户{client_id}的任务列表 ===")

        # 查询该客户下有待上传图片文案的任务ID
        task_ids = db.session.query(Content.task_id.distinct()).filter(
            Content.client_id == client_id,
            Content.workflow_status.in_(['first_reviewed', 'image_uploaded']),
            Content.is_deleted != True
        ).all()

        task_ids = [task_id[0] for task_id in task_ids if task_id[0]]
        print(f"找到有待上传图片文案的任务ID: {task_ids}")

        if not task_ids:
            print("该客户没有待上传图片的任务")
            return jsonify({
                'success': True,
                'tasks': []
            })

        # 查询对应的任务
        tasks = Task.query.filter(
            Task.id.in_(task_ids),
            Task.client_id == client_id
        ).order_by(Task.name).all()

        print(f"查询到 {len(tasks)} 个任务")
        for task in tasks:
            print(f"任务: {task.id} - {task.name}")

        # 转换为JSON格式
        tasks_data = []
        for task in tasks:
            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'client_id': task.client_id
            })

        return jsonify({
            'success': True,
            'tasks': tasks_data
        })

    except Exception as e:
        print(f"获取客户任务列表失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        })


@main_simple_bp.route('/api/get-final-review-client-tasks/<int:client_id>')
def get_final_review_client_tasks(client_id):
    """获取指定客户的任务列表（只包含有最终审核文案的任务）"""
    try:
        from app.models.content import Content
        from app.models.task import Task

        print(f"=== 获取客户{client_id}的最终审核任务列表 ===")

        # 查询该客户下有最终审核文案的任务ID
        task_ids = db.session.query(Content.task_id.distinct()).filter(
            Content.client_id == client_id,
            Content.workflow_status == 'final_review',
            Content.is_deleted != True
        ).all()

        task_ids = [task_id[0] for task_id in task_ids if task_id[0]]
        print(f"找到有最终审核文案的任务ID: {task_ids}")

        if not task_ids:
            print("该客户没有最终审核的任务")
            return jsonify({
                'success': True,
                'tasks': []
            })

        # 查询对应的任务
        tasks = Task.query.filter(
            Task.id.in_(task_ids),
            Task.client_id == client_id
        ).order_by(Task.name).all()

        print(f"查询到 {len(tasks)} 个任务")
        for task in tasks:
            print(f"任务: {task.id} - {task.name}")

        # 转换为JSON格式
        tasks_data = []
        for task in tasks:
            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'client_id': task.client_id
            })

        return jsonify({
            'success': True,
            'tasks': tasks_data
        })

    except Exception as e:
        print(f"获取客户最终审核任务列表失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        })


@main_simple_bp.route('/api/upload/image', methods=['POST'])
@login_required
@csrf.exempt  # 跳过CSRF验证
def upload_image():
    """上传图片API"""
    print(f"=== 图片上传API被调用 ===")
    print(f"请求方法: {request.method}")
    print(f"请求路径: {request.path}")
    print(f"表单数据: {dict(request.form)}")
    print(f"文件数据: {list(request.files.keys())}")

    # 先导入必要的模块，避免在异常处理中出现UnboundLocalError
    from app.models.content import Content
    from app.models.image import ContentImage
    from app import db
    from datetime import datetime
    import os

    try:
        from app.utils.image_handler import ImageUploadHandler

        # 获取参数
        content_id = request.form.get('content_id')
        if not content_id:
            return jsonify({'success': False, 'message': '缺少文案ID'}), 400

        # 验证文案是否存在且状态正确
        content = Content.query.get(content_id)
        if not content:
            return jsonify({'success': False, 'message': '文案不存在'}), 404

        if content.workflow_status not in ['first_reviewed', 'image_uploaded']:
            return jsonify({'success': False, 'message': '文案状态不正确，只能为初审通过的文案上传图片'}), 400

        # 检查是否有上传的文件
        if 'file' not in request.files:
            print("错误: 请求中没有file字段")
            return jsonify({'success': False, 'message': '没有选择文件'}), 400

        file = request.files['file']
        print(f"文件信息: filename={file.filename}, content_type={file.content_type}")

        if file.filename == '':
            print("错误: 文件名为空")
            return jsonify({'success': False, 'message': '没有选择文件'}), 400

        # 检查图片数量限制
        from app.models.system_setting import SystemSetting
        max_images = int(SystemSetting.get_value('MAX_IMAGES_PER_CONTENT', 9))
        current_count = ContentImage.get_active_count(content_id)

        if current_count >= max_images:
            return jsonify({'success': False, 'message': f'每篇文案最多只能上传{max_images}张图片'}), 400

        # 初始化图片处理器（自动从系统设置读取配置）
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
        handler = ImageUploadHandler(upload_folder)

        # 保存图片
        print(f"开始保存图片: {file.filename}")
        image_info = handler.save_image(file, content_id)
        print(f"图片保存成功: {image_info}")

        # 保存到数据库
        content_image = ContentImage(
            content_id=content_id,
            image_path=image_info['image_path'],
            thumbnail_path=image_info['thumbnail_path'],
            original_name=image_info['original_name'],
            file_size=image_info['file_size'],
            image_order=ContentImage.get_next_order(content_id),
            upload_time=datetime.now()
        )

        db.session.add(content_image)

        # 更新文案状态
        if current_count == 0:  # 第一张图片
            content.workflow_status = 'image_uploaded'
            # 重置审核状态（清除之前的驳回状态）
            if content.internal_review_status in ['final_rej_img', 'final_rej_both', 'final_rej_text']:
                content.internal_review_status = 'pending'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '图片上传成功',
            'image': content_image.to_dict()
        })

    except ValueError as e:
        print(f"=== ValueError异常 ===")
        print(f"异常信息: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 400
    except Exception as e:
        print(f"=== 图片上传API异常 ===")
        print(f"异常类型: {type(e).__name__}")
        print(f"异常信息: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        db.session.rollback()
        return jsonify({'success': False, 'message': f'上传失败：{str(e)}'}), 500


@main_simple_bp.route('/api/images/<int:content_id>')
@login_required
def get_content_images(content_id):
    """获取文案的图片列表"""
    try:
        from app.models.image import ContentImage

        images = ContentImage.get_by_content(content_id)
        return jsonify({
            'success': True,
            'images': [img.to_dict() for img in images]
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@main_simple_bp.route('/api/contents/<int:content_id>')
@login_required
def get_content_detail(content_id):
    """获取文案详情API"""
    try:
        from app.models.content import Content
        import json

        content = Content.query.get_or_404(content_id)

        # 解析话题
        topics = []
        if content.topics:
            try:
                topics = json.loads(content.topics)
            except:
                topics = []

        # 解析@用户
        at_users = []
        if content.ext_json:
            try:
                ext_data = json.loads(content.ext_json)
                at_users = ext_data.get('at_users', [])
            except:
                at_users = []

        return jsonify({
            'success': True,
            'content': {
                'id': content.id,
                'title': content.title,
                'content': content.content,
                'topics': topics,
                'at_users': at_users,
                'location': content.location,
                'client_name': content.client.name if content.client else None,
                'created_at': content.created_at.strftime('%Y-%m-%d %H:%M'),
                'workflow_status': content.workflow_status,
                'client_review_status': content.client_review_status,
                'internal_review_status': content.internal_review_status
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@main_simple_bp.route('/api/images/<int:image_id>', methods=['DELETE'])
@login_required
@csrf.exempt  # 跳过CSRF验证
def delete_image(image_id):
    """删除图片API"""
    try:
        from app.models.image import ContentImage
        from app.utils.image_handler import ImageUploadHandler
        from app import db
        import os

        # 获取图片记录
        image = ContentImage.query.get_or_404(image_id)

        # 检查权限（可以添加更多权限检查）
        content = image.content
        if content.workflow_status not in ['first_reviewed', 'image_uploaded']:
            return jsonify({
                'success': False,
                'message': '当前状态不允许删除图片'
            }), 400

        # 删除物理文件
        upload_folder = os.path.join(current_app.root_path, 'static', 'uploads')
        handler = ImageUploadHandler(upload_folder)
        handler.delete_image(image.image_path, image.thumbnail_path)

        # 标记为已删除
        image.mark_deleted()

        # 检查是否还有其他图片，如果没有则更新文案状态
        remaining_images = ContentImage.get_active_count(content.id)
        if remaining_images == 0:
            content.workflow_status = 'first_reviewed'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '图片删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败：{str(e)}'}), 500


@main_simple_bp.route('/api/images/reorder', methods=['POST'])
@login_required
@csrf.exempt
def reorder_images():
    """重新排序图片API"""
    try:
        from app.models.image import ContentImage
        from app import db

        # 获取参数
        data = request.get_json()
        if not data or 'image_orders' not in data:
            return jsonify({'success': False, 'message': '缺少排序数据'}), 400

        image_orders = data['image_orders']  # 格式: [{'id': 1, 'order': 1}, {'id': 2, 'order': 2}, ...]

        print(f"=== 图片排序API被调用 ===")
        print(f"排序数据: {image_orders}")

        # 批量更新图片顺序
        updated_count = 0
        for item in image_orders:
            image_id = item.get('id')
            new_order = item.get('order')

            if image_id and new_order is not None:
                image = ContentImage.query.get(image_id)
                if image:
                    print(f"更新图片 {image_id} 的顺序从 {image.image_order} 到 {new_order}")
                    image.image_order = new_order
                    updated_count += 1

        db.session.commit()
        print(f"成功更新 {updated_count} 张图片的顺序")

        return jsonify({
            'success': True,
            'message': f'图片顺序更新成功，共更新{updated_count}张图片'
        })

    except Exception as e:
        print(f"图片排序失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'message': f'更新失败：{str(e)}'}), 500


@main_simple_bp.route('/api/contents/<int:content_id>/submit', methods=['POST'])
@login_required
@csrf.exempt
def submit_content(content_id):
    """提交内容到最终审核"""
    try:
        from app.models.content import Content
        from app.models.image import ContentImage
        from datetime import datetime

        # 获取内容
        content = Content.query.get_or_404(content_id)

        # 检查权限
        if not current_user.has_permission('content_manage'):
            return jsonify({'success': False, 'message': '您没有权限执行此操作'}), 403

        # 检查当前状态
        valid_statuses = ['first_reviewed', 'image_uploaded']

        # 特殊处理：如果是终审驳回的"两者都有问题"状态，也允许提交
        if content.internal_review_status == 'final_rej_both' and content.workflow_status == 'draft':
            # 这是"两者都有问题"的驳回状态，允许提交
            pass
        # 特殊处理：如果是客户驳回状态，也允许提交
        elif content.client_review_status == 'rejected' and content.workflow_status == 'draft':
            # 这是客户驳回状态，允许提交
            pass
        elif content.workflow_status not in valid_statuses:
            return jsonify({'success': False, 'message': '当前状态不允许提交'}), 400

        # 检查是否已上传图片
        images = ContentImage.get_by_content(content_id)
        if not images:
            return jsonify({'success': False, 'message': '请先上传图片再提交'}), 400

        # 特殊处理：如果是客户驳回状态
        if content.client_review_status == 'rejected':
            # 客户驳回后重新提交文案，使用类似最终审核驳回的逻辑
            if content.internal_review_status == 'client_rej_text_ok':
                # 驳回状态：图片已修复，文案审核通过，检查是否都完成
                if content.image_completed == 1:
                    # 图片已完成，文案也审核通过，直接进入终审
                    content.workflow_status = 'final_review'
                    content.client_review_status = 'pending'  # 重置客户审核状态
                    content.internal_review_status = 'pending'  # 重置内部审核状态
                    content.content_completed = 1
                    message = '客户驳回状态：图片和文案都已完成，进入终审'
                else:
                    # 图片未完成，文案审核通过，等待图片处理
                    content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                    content.internal_review_status = 'client_rej_img'  # 改为等待图片处理
                    content.content_completed = 1
                    message = '客户驳回状态：文案审核通过，等待图片处理'
            elif content.internal_review_status == 'client_rej_both':
                # 客户驳回（两者都有问题）：图片提交后，只标记图片完成，不改变workflow_status
                content.image_completed = 1
                # 保持workflow_status为'draft'，让文案继续在初审页面显示
                content.internal_review_status = 'client_rej_text'  # 改为文案问题
                message = '图片已重新上传，请继续编辑文案'
            elif content.internal_review_status == 'client_rej_text':
                # 客户驳回（文案问题）：文案提交后，检查图片状态
                if content.image_completed == 1:
                    # 图片已完成，文案也审核通过，根据系统设置决定下一步
                    from app.models.system_setting import SystemSetting

                    # 检查是否启用最终审核
                    enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')

                    if enable_final_review == '1':
                        # 启用最终审核，进入最终审核阶段
                        content.workflow_status = 'final_review'
                        content.client_review_status = 'pending'  # 重置客户审核状态
                        content.internal_review_status = 'pending'  # 重置内部审核状态
                        message = '客户驳回状态：图片和文案都已完成，进入终审'
                    else:
                        # 关闭最终审核，直接进入客户审核阶段
                        from app.models.client import Client
                        client = Client.query.get(content.client_id)

                        if client and client.need_review:
                            # 需要客户审核
                            content.workflow_status = 'pending_client_review'
                            content.client_review_status = 'pending'
                            content.internal_review_status = 'final_approved'
                            message = '客户驳回状态：图片和文案都已完成，关闭最终审核，直接进入客户审核'
                        else:
                            # 不需要客户审核，直接进入待发布
                            content.workflow_status = 'pending_publish'
                            content.client_review_status = 'approved'
                            content.internal_review_status = 'final_approved'
                            message = '客户驳回状态：图片和文案都已完成，关闭最终审核，直接进入待发布'

                    content.content_completed = 1
                else:
                    # 图片未完成，文案审核通过，等待图片处理
                    content.workflow_status = 'first_reviewed'  # 修改为first_reviewed以便在图片管理页面显示
                    content.internal_review_status = 'client_rej_img'  # 改为等待图片处理
                    content.content_completed = 1
                    message = '客户驳回状态：文案审核通过，等待图片处理'
            else:
                # 其他情况，直接进入终审（兼容旧逻辑）
                content.workflow_status = 'final_review'
                content.client_review_status = 'pending'  # 重置客户审核状态
                content.internal_review_status = 'pending'  # 重置内部审核状态
                content.content_completed = 1
                content.image_completed = 1
                message = '客户驳回后重新编辑完成，已提交最终审核'
        # 特殊处理：如果是终审驳回的"两者都有问题"状态
        elif content.internal_review_status == 'final_rej_both':
            # 标记图片已完成，取消图片驳回状态
            content.image_completed = 1

            # 检查文案是否也已完成
            if content.content_completed == 1:
                # 文案也已完成，根据系统设置决定下一步
                from app.models.system_setting import SystemSetting

                # 检查是否启用最终审核
                enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')

                if enable_final_review == '1':
                    # 启用最终审核，进入最终审核阶段
                    content.workflow_status = 'final_review'
                    content.internal_review_status = 'pending'
                    message = '文案和图片都已重新编辑，已提交最终审核'
                else:
                    # 关闭最终审核，直接进入客户审核阶段
                    from app.models.client import Client
                    client = Client.query.get(content.client_id)

                    if client and client.need_review:
                        # 需要客户审核
                        content.workflow_status = 'pending_client_review'
                        content.client_review_status = 'pending'
                        content.internal_review_status = 'final_approved'
                        message = '文案和图片都已重新编辑，关闭最终审核，直接进入客户审核'
                    else:
                        # 不需要客户审核，直接进入待发布
                        content.workflow_status = 'pending_publish'
                        content.client_review_status = 'approved'
                        content.internal_review_status = 'final_approved'
                        message = '文案和图片都已重新编辑，关闭最终审核，直接进入待发布'
            else:
                # 文案还未完成，但图片已修复 - 使用新的精确状态
                # 改为文案驳回状态，让图片从图片管理页面消失
                content.workflow_status = 'draft'
                content.internal_review_status = 'final_rej_text_ok'  # 文案问题但图片已修复
                message = '图片已重新上传，请继续编辑文案后再提交'
        # 特殊处理：如果是只有图片问题的驳回状态
        elif content.internal_review_status == 'final_rej_img':
            # 标记图片已完成，取消图片驳回状态
            content.image_completed = 1

            # 图片问题已解决，根据系统设置决定下一步
            from app.models.system_setting import SystemSetting

            # 检查是否启用最终审核
            enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')

            if enable_final_review == '1':
                # 启用最终审核，进入最终审核阶段
                content.workflow_status = 'final_review'
                content.internal_review_status = 'pending'
                message = '图片已重新上传，已提交最终审核'
            else:
                # 关闭最终审核，直接进入客户审核阶段
                from app.models.client import Client
                client = Client.query.get(content.client_id)

                if client and client.need_review:
                    # 需要客户审核
                    content.workflow_status = 'pending_client_review'
                    content.client_review_status = 'pending'
                    content.internal_review_status = 'final_approved'
                    message = '图片已重新上传，关闭最终审核，直接进入客户审核'
                else:
                    # 不需要客户审核，根据系统发布设置决定状态
                    from app.models.system_setting import SystemSetting
                    auto_publish_enabled = SystemSetting.get_value('auto_publish_enabled', 'false')

                    if auto_publish_enabled.lower() in ['false', '0']:
                        # 关闭手动发布开关，自动发布，直接进入待发布状态
                        content.workflow_status = 'pending_publish'
                        content.publish_status = 'pending_publish'
                        message = '图片已重新上传，关闭最终审核，已自动提交发布'
                    else:
                        # 开启手动发布开关，进入准备发布状态，需要手动提交
                        content.workflow_status = 'ready_to_publish'
                        message = '图片已重新上传，关闭最终审核，等待手动发布'

                    content.client_review_status = 'approved'
                    content.internal_review_status = 'final_approved'
        else:
            # 正常流程：图片上传完成，根据系统设置决定下一步
            from app.models.system_setting import SystemSetting

            # 检查是否启用最终审核
            enable_final_review = SystemSetting.get_value('ENABLE_FINAL_REVIEW', '1')

            if enable_final_review == '1':
                # 启用最终审核，进入最终审核阶段
                content.workflow_status = 'final_review'
                content.internal_review_status = 'pending'
                message = '图片已提交，进入最终审核'
                print(f"启用最终审核开关，图片上传后状态设置为: final_review")
            else:
                # 关闭最终审核，直接进入客户审核阶段
                from app.models.client import Client
                client = Client.query.get(content.client_id)

                if client and client.need_review:
                    # 需要客户审核
                    content.workflow_status = 'pending_client_review'
                    content.client_review_status = 'pending'
                    content.internal_review_status = 'final_approved'  # 标记为最终审核已通过
                    message = '图片已提交，关闭最终审核，直接进入客户审核'
                    print(f"关闭最终审核开关，需要客户审核，状态设置为: pending_client_review")
                else:
                    # 不需要客户审核，直接进入待发布
                    content.workflow_status = 'pending_publish'
                    content.client_review_status = 'approved'  # 自动设为客户已通过
                    content.internal_review_status = 'final_approved'  # 标记为最终审核已通过
                    message = '图片已提交，关闭最终审核，直接进入待发布'
                    print(f"关闭最终审核开关，不需要客户审核，状态设置为: pending_publish")

            content.image_completed = 1

        content.status_update_time = datetime.now()

        # 提交数据库更改
        db.session.commit()

        return jsonify({'success': True, 'message': message})

    except Exception as e:
        db.session.rollback()
        print(f"提交内容失败: {str(e)}")
        return jsonify({'success': False, 'message': f'提交失败: {str(e)}'}), 500


@main_simple_bp.route('/final-review')
@login_required
@menu_permission_required('/simple/final-review')
def final_review():
    """最终审核页面"""
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回页面内容
        from app.models.content import Content
        from app.models.image import ContentImage
        from app.models.client import Client
        from app.models.task import Task
        from app.forms.content import ContentFilterForm

        # 获取筛选表单
        form = ContentFilterForm()

        # 获取有最终审核文案的客户列表（只显示在最终审核流程中有文案的客户）
        from sqlalchemy import and_
        clients_with_tasks = db.session.query(Client).join(Content).filter(
            and_(
                Content.workflow_status == 'final_review',  # 修复：应该是final_review状态的文案
                Content.is_deleted == False,
                Client.status == True
            )
        ).distinct().order_by(Client.name).all()

        # 初始化表单选择项
        form.client_id.choices = [(0, '全部客户')] + [(c.id, c.name) for c in clients_with_tasks]

        # 根据是否选择了客户来决定任务列表
        selected_client_id = request.args.get('client_id', type=int)
        if selected_client_id:
            # 获取该客户在最终审核流程中的任务
            tasks_with_content = db.session.query(Task).join(Content).filter(
                and_(
                    Content.client_id == selected_client_id,
                    Content.workflow_status == 'final_review',  # 修复：应该是final_review状态的文案
                    Content.is_deleted == False
                )
            ).distinct().order_by(Task.name).all()
            form.task_id.choices = [(0, '全部任务')] + [(t.id, t.name) for t in tasks_with_content]
        else:
            # 没有选择客户时，任务列表为空
            form.task_id.choices = [(0, '请先选择客户')]

        # 构建查询条件 - 查询待最终审核的文案（支持两种状态）
        query = Content.query.filter(Content.workflow_status.in_(['image_uploaded', 'final_review']))

        # 应用筛选条件
        if selected_client_id:
            query = query.filter_by(client_id=selected_client_id)

        selected_task_id = request.args.get('task_id', type=int)
        if selected_task_id:
            query = query.filter_by(task_id=selected_task_id)

        # 注意：ContentFilterForm 没有 batch_id 字段，所以不处理批次筛选

        # 分页
        page = request.args.get('page', 1, type=int)
        per_page = 10

        contents = query.order_by(Content.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 为每个文案获取图片信息
        content_data = []
        for content in contents.items:
            images = ContentImage.get_by_content(content.id)
            content_data.append({
                'content': content,
                'images': images,
                'image_count': len(images)
            })

        return render_template('review/final_review.html',
                             content_data=content_data,
                             pagination=contents,
                             form=form)
    else:
        # 非AJAX请求，返回完整页面
        from app.models.content import Content
        from app.models.image import ContentImage
        from app.models.client import Client
        from app.models.task import Task
        from app.forms.content import ContentFilterForm

        # 获取筛选表单
        form = ContentFilterForm()

        # 获取有最终审核文案的客户列表
        from sqlalchemy import and_
        clients_with_tasks = db.session.query(Client).join(Content).filter(
            and_(
                Content.workflow_status == 'final_review',
                Content.is_deleted == False,
                Client.status == True
            )
        ).distinct().order_by(Client.name).all()

        # 初始化表单选择项
        form.client_id.choices = [(0, '全部客户')] + [(c.id, c.name) for c in clients_with_tasks]
        form.task_id.choices = [(0, '请先选择客户')]

        # 构建查询条件
        query = Content.query.filter_by(workflow_status='final_review')

        # 分页
        page = request.args.get('page', 1, type=int)
        per_page = 10

        contents = query.order_by(Content.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 为每个文案获取图片信息
        content_data = []
        for content in contents.items:
            images = ContentImage.get_by_content(content.id)
            content_data.append({
                'content': content,
                'images': images,
                'image_count': len(images)
            })

        # 非AJAX请求，返回完整页面布局
        return render_template('main_simple.html',
                             page_content=render_template('review/final_review.html',
                                                        content_data=content_data,
                                                        pagination=contents,
                                                        form=form),
                             current_page='final-review')


@main_simple_bp.route('/api/contents/<int:content_id>/view')
@login_required
def view_content_api(content_id):
    """查看文案详情API"""
    try:
        from app.models.content import Content, RejectionReason
        from app.models.image import ContentImage

        content = Content.query.get_or_404(content_id)
        images = ContentImage.get_by_content(content_id)

        # 获取驳回历史，按时间倒序
        rejections = RejectionReason.query.filter_by(content_id=content_id)\
                                         .order_by(RejectionReason.created_at.desc())\
                                         .all()

        # 计算字符统计 - 直接使用字符串长度
        title_length = len(content.title or '')
        content_length = len(content.content or '')

        # 判断是否符合要求
        title_status = 'success' if title_length <= 20 else 'danger'
        content_status = 'success' if content_length <= 1000 else 'danger'

        print(f"字符统计 - 标题: {title_length}/20 ({title_status}), 内容: {content_length}/1000 ({content_status})")

        return render_template('content/view_modal.html',
                             content=content,
                             images=images,
                             rejections=rejections,
                             title_length=title_length,
                             content_length=content_length,
                             title_status=title_status,
                             content_status=content_status)

    except Exception as e:
        return f'<div class="alert alert-danger">加载失败：{str(e)}</div>', 500


@main_simple_bp.route('/fix-emoji-charset')
@login_required
def fix_emoji_charset():
    """修复emoji字符集问题 - 将数据库和表字符集改为utf8mb4"""
    try:
        import pymysql
        from app import db

        # 获取数据库连接配置
        engine = db.engine
        connection_info = engine.url

        # 创建原生连接
        connection = pymysql.connect(
            host=connection_info.host,
            port=connection_info.port or 3306,
            user=connection_info.username,
            password=connection_info.password,
            database=connection_info.database,
            charset='utf8mb4'
        )

        results = []

        try:
            with connection.cursor() as cursor:
                # 1. 修改数据库字符集
                cursor.execute(f"ALTER DATABASE {connection_info.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                results.append("数据库字符集已修改为utf8mb4")

                # 2. 修改templates表字符集
                cursor.execute("ALTER TABLE templates CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                results.append("templates表字符集已修改为utf8mb4")

                # 3. 确保marks字段也是utf8mb4
                cursor.execute("""
                    ALTER TABLE templates
                    MODIFY COLUMN marks JSON
                    CHARACTER SET utf8mb4
                    COLLATE utf8mb4_unicode_ci
                """)
                results.append("marks字段字符集已修改为utf8mb4")

                # 4. 提交更改
                connection.commit()
                results.append("所有更改已提交")

                # 5. 验证修改结果
                cursor.execute("SELECT @@character_set_database, @@collation_database")
                db_charset = cursor.fetchone()
                results.append(f"数据库字符集: {db_charset[0]}, 排序规则: {db_charset[1]}")

                cursor.execute("""
                    SELECT COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = %s
                    AND TABLE_NAME = 'templates'
                    AND COLUMN_NAME IN ('title', 'content', 'marks')
                """, (connection_info.database,))

                field_info = cursor.fetchall()
                results.append("字段字符集信息:")
                for field in field_info:
                    results.append(f"  {field[0]}: {field[1]} / {field[2]}")

        finally:
            connection.close()

        return jsonify({
            'success': True,
            'message': 'emoji字符集修复完成',
            'details': results
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/restore-emoji-content')
@login_required
def restore_emoji_content():
    """恢复模板中被损坏的emoji字符"""
    try:
        from app.models.template import Template
        from app import db

        # 常见的emoji映射表（问号 -> emoji）
        emoji_mapping = {
            '?': '✅',  # 最常见的打勾符号
            '??': '✨',  # 星星
            '???': '☀️',  # 太阳
            '????': '🔥',  # 火焰
            '?????': '💕',  # 爱心
        }

        # 根据上下文智能恢复emoji
        context_emoji_mapping = {
            '必点单品！': '✅',
            '一口就爱上': '✅',
            '挖到宝了': '✨',
            '开启元气一天': '☀️',
            '太罪恶了': '✅',
            '也太香了吧！': '✨',
        }

        templates = Template.query.all()
        updated_count = 0
        results = []

        for template in templates:
            title_updated = False
            content_updated = False
            original_title = template.title
            original_content = template.content

            # 恢复标题中的emoji
            if template.title and '?' in template.title:
                new_title = template.title

                # 基于上下文恢复
                for context, emoji in context_emoji_mapping.items():
                    if context in new_title and '?' in new_title:
                        # 找到上下文附近的问号并替换
                        import re
                        pattern = r'\?' + re.escape(context)
                        if re.search(pattern, new_title):
                            new_title = re.sub(r'\?(?=' + re.escape(context) + ')', emoji, new_title)

                        pattern = re.escape(context) + r'\?'
                        if re.search(pattern, new_title):
                            new_title = re.sub(re.escape(context) + r'\?', context + emoji, new_title)

                # 处理末尾的单个问号
                if new_title.endswith('? '):
                    new_title = new_title[:-2] + '✅ '
                elif new_title.endswith('?'):
                    new_title = new_title[:-1] + '✅'

                if new_title != template.title:
                    template.title = new_title
                    title_updated = True

            # 恢复内容中的emoji
            if template.content and '?' in template.content:
                new_content = template.content

                # 处理行首的问号（通常是✅）
                import re
                new_content = re.sub(r'^(\s*)\?(\s)', r'\1✅\2', new_content, flags=re.MULTILINE)

                # 处理特定模式
                patterns = [
                    (r'路过被香味吸引进店，([^！]+)直接封神！', r'路过被香味吸引进店，\1直接封神！'),
                    (r'吃完立刻安利给闺蜜\?', r'吃完立刻安利给闺蜜💕'),
                    (r'打工人早餐首选', r'打工人早餐首选'),
                ]

                for pattern, replacement in patterns:
                    new_content = re.sub(pattern, replacement, new_content)

                if new_content != template.content:
                    template.content = new_content
                    content_updated = True

            if title_updated or content_updated:
                # 重新提取标记
                template.update_marks()
                updated_count += 1

                results.append({
                    'id': template.id,
                    'title_changed': title_updated,
                    'content_changed': content_updated,
                    'original_title': original_title if title_updated else None,
                    'new_title': template.title if title_updated else None,
                    'original_content': original_content[:100] + '...' if content_updated and len(original_content) > 100 else original_content if content_updated else None,
                    'new_content': template.content[:100] + '...' if content_updated and len(template.content) > 100 else template.content if content_updated else None
                })

        # 提交更改
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'已恢复 {updated_count} 个模板的emoji字符',
            'updated_count': updated_count,
            'details': results
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@main_simple_bp.route('/api/tasks/all-with-content')
@login_required
def get_all_tasks_with_content():
    """获取所有有待初审文案的任务"""
    try:
        from app.models.task import Task
        from app.models.content import Content
        from sqlalchemy import and_

        tasks = db.session.query(Task).join(Content).filter(
            and_(
                Content.workflow_status.in_(['draft', 'pending_review']),
                Content.is_deleted == False
            )
        ).distinct().order_by(Task.name).all()

        return jsonify({
            'success': True,
            'tasks': [{'id': task.id, 'name': task.name} for task in tasks]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/api/tasks/by-client/<int:client_id>')
@login_required
def get_tasks_by_client(client_id):
    """获取指定客户下有待初审文案的任务"""
    try:
        from app.models.task import Task
        from app.models.content import Content
        from sqlalchemy import and_

        tasks = db.session.query(Task).join(Content).filter(
            and_(
                Content.client_id == client_id,
                Content.workflow_status.in_(['draft', 'pending_review']),
                Content.is_deleted == False
            )
        ).distinct().order_by(Task.name).all()

        return jsonify({
            'success': True,
            'tasks': [{'id': task.id, 'name': task.name} for task in tasks]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@main_simple_bp.route('/api/contents/<int:content_id>/edit')
@login_required
def edit_content_api(content_id):
    """编辑文案API"""
    try:
        from app.models.content import Content, RejectionReason
        from app.forms.content import EditContentForm

        content = Content.query.get_or_404(content_id)
        form = EditContentForm(obj=content)

        # 预加载驳回理由，按时间倒序排列
        rejection_reasons = content.rejection_reasons.order_by(RejectionReason.created_at.desc()).all()

        return render_template('content/edit_modal.html',
                             content=content,
                             form=form,
                             rejection_reasons=rejection_reasons)

    except Exception as e:
        return f'<div class="alert alert-danger">加载失败：{str(e)}</div>', 500


@main_simple_bp.route('/api/contents/<int:content_id>/delete', methods=['POST'])
@login_required
@csrf.exempt  # 跳过CSRF验证
def delete_content_api(content_id):
    """删除文案API"""
    try:
        print(f"=== 删除文案API被调用，content_id: {content_id} ===")
        print(f"请求方法: {request.method}")
        print(f"请求路径: {request.path}")
        print(f"请求头: {dict(request.headers)}")
        print(f"表单数据: {dict(request.form)}")
        print(f"用户认证状态: {current_user.is_authenticated if current_user else 'No current_user'}")

        from app.models.content import Content
        from app import db

        content = Content.query.get_or_404(content_id)

        # 检查权限（可以添加更多权限检查）
        if content.workflow_status in ['published']:
            return jsonify({
                'success': False,
                'message': '已发布的文案不能删除'
            }), 400

        # 软删除
        content.is_deleted = True
        content.deleted_at = datetime.now()
        content.deleted_by = current_user.id

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文案删除成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除失败：{str(e)}'
        }), 500


# 测试路由
@main_simple_bp.route('/api/test', methods=['GET', 'POST'])
def test_api():
    """测试API路由"""
    print(f"=== 测试API被调用 ===")
    print(f"请求方法: {request.method}")
    print(f"请求路径: {request.path}")
    return jsonify({
        'success': True,
        'message': '测试API工作正常',
        'method': request.method,
        'path': request.path
    })

# 测试GET路由
@main_simple_bp.route('/api/content-update/<int:content_id>', methods=['GET'])
def content_update_get_test(content_id):
    """测试GET路由"""
    print(f"=== GET测试路由被调用，content_id: {content_id} ===")
    return jsonify({
        'success': True,
        'message': 'GET路由工作正常',
        'content_id': content_id
    })

# 新的更新路由，使用不同的路径
@main_simple_bp.route('/api/content-update/<int:content_id>', methods=['POST'])
@csrf.exempt  # 跳过CSRF验证
def content_update_new(content_id):
    """新的更新文案API"""
    print(f"=== 函数开始执行，content_id: {content_id} ===")

    try:
        # 手动跳过CSRF验证
        from flask import g
        g._csrf_disabled = True

        print(f"=== 新更新文案API被调用，content_id: {content_id} ===")
        print(f"请求方法: {request.method}")
        print(f"请求路径: {request.path}")
        print(f"请求头: {dict(request.headers)}")
        print(f"表单数据: {dict(request.form)}")
        print(f"用户认证状态: {current_user.is_authenticated if current_user else 'No current_user'}")

        # 检查用户认证
        if not current_user.is_authenticated:
            print("用户未认证，返回401")
            return jsonify({'error': '用户未认证'}), 401

        # 继续执行实际的更新逻辑，而不是立即返回
        from app.models.content import Content
        from app import db
        from datetime import datetime, time
        import json

        content = Content.query.get_or_404(content_id)

        # 检查权限（可以添加更多权限检查）
        if content.workflow_status in ['published']:
            return jsonify({
                'success': False,
                'message': '已发布的文案不能编辑'
            }), 400

        # 更新基本信息
        content.title = request.form.get('title', '').strip()
        content.content = request.form.get('content', '').strip()
        content.location = request.form.get('location', '').strip()
        content.publish_priority = request.form.get('publish_priority', 'normal')

        # 字符限制验证
        def calculate_title_length(text):
            """计算标题长度：字母数字算0.5个，emoji算1个"""
            length = 0
            for char in text:
                if ord(char) > 127:
                    # 检查是否是中文字符
                    if '\u4e00' <= char <= '\u9fff':
                        length += 1  # 中文字符算1个
                    else:
                        length += 1  # emoji算1个
                else:
                    length += 0.5  # 字母数字算0.5个
            return int(length + 0.5)  # 四舍五入

        def calculate_content_length(text):
            """计算内容长度：中文算2个，其他算1个"""
            length = 0
            for char in text:
                if '\u4e00' <= char <= '\u9fff':
                    length += 2  # 中文字符算2个
                else:
                    length += 1  # 其他字符算1个
            return length

        # 验证标题长度
        title_length = calculate_title_length(content.title)
        if title_length > 20:
            return jsonify({
                'success': False,
                'message': f'标题超出限制！当前{title_length}个字符，最多20个字符'
            }), 400

        # 验证内容长度
        content_length = calculate_content_length(content.content)
        if content_length > 1000:
            return jsonify({
                'success': False,
                'message': f'内容超出限制！当前{content_length}个字符，最多1000个字符'
            }), 400

        # 处理话题数据
        topics_str = request.form.get('topics', '').strip()
        if topics_str:
            # 将换行分隔的话题转换为JSON格式
            topics_list = [t.strip() for t in topics_str.split('\n') if t.strip()]
            content.topics = json.dumps(topics_list, ensure_ascii=False)
        else:
            content.topics = None

        # 处理@用户数据
        at_users_str = request.form.get('at_users', '').strip()
        if at_users_str:
            # 将换行分隔的@用户转换为列表并保存到扩展数据
            at_users_list = [u.strip() for u in at_users_str.split('\n') if u.strip()]
            # 获取现有的扩展数据
            ext_data = json.loads(content.ext_json) if content.ext_json else {}
            ext_data['at_users'] = at_users_list
            content.ext_json = json.dumps(ext_data, ensure_ascii=False)
        else:
            # 清空@用户数据
            ext_data = json.loads(content.ext_json) if content.ext_json else {}
            if 'at_users' in ext_data:
                del ext_data['at_users']
            content.ext_json = json.dumps(ext_data, ensure_ascii=False) if ext_data else None

        # 更新发布时间 - 已移除时间设置功能
        # display_date_str = request.form.get('display_date', '').strip()
        # display_time_str = request.form.get('display_time', '').strip()

        # if display_date_str:
        #     try:
        #         display_date = datetime.strptime(display_date_str, '%Y-%m-%d').date()
        #         content.display_date = display_date
        #     except ValueError:
        #         pass

        # if display_time_str:
        #     try:
        #         display_time = datetime.strptime(display_time_str, '%H:%M').time()
        #         content.display_time = display_time
        #     except ValueError:
        #         pass

        # 更新修改时间
        content.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文案更新成功'
        })

    except Exception as e:
        print(f"=== 新API异常 ===")
        print(f"异常类型: {type(e).__name__}")
        print(f"异常信息: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新失败：{str(e)}'
        }), 500

@main_simple_bp.route('/api/contents/<int:content_id>/update', methods=['POST'])
def update_content_api(content_id):
    """更新文案API"""
    try:
        print(f"=== 更新文案API被调用，content_id: {content_id} ===")
        print(f"请求方法: {request.method}")
        print(f"请求路径: {request.path}")
        print(f"请求头: {dict(request.headers)}")
        print(f"表单数据: {dict(request.form)}")
        print(f"用户认证状态: {current_user.is_authenticated if current_user else 'No current_user'}")

        # 检查用户认证
        if not current_user.is_authenticated:
            print("用户未认证，返回401")
            return jsonify({'error': '用户未认证'}), 401

        # 立即返回成功响应进行测试
        return jsonify({
            'success': True,
            'message': '更新成功',
            'content_id': content_id,
            'received_data': dict(request.form),
            'user_id': current_user.id
        })
    except Exception as e:
        print(f"=== API异常 ===")
        print(f"异常类型: {type(e).__name__}")
        print(f"异常信息: {str(e)}")
        import traceback
        print(f"异常堆栈: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': f'API异常：{str(e)}'
        }), 500




@main_simple_bp.route('/api/final-review/<int:content_id>', methods=['POST'])
@login_required
@csrf.exempt  # 跳过CSRF验证
def final_review_content(content_id):
    """最终审核单个文案"""
    try:
        from app.models.content import Content
        from app.models.system_setting import SystemSetting
        from app import db

        print(f"=== 最终审核API被调用，content_id: {content_id} ===")

        content = Content.query.get_or_404(content_id)
        print(f"文案当前状态: {content.workflow_status}")

        # 检查当前状态是否可以审核（支持两种状态）
        if content.workflow_status not in ['image_uploaded', 'final_review']:
            print(f"状态检查失败: {content.workflow_status}")
            return jsonify({
                'success': False,
                'message': f'文案状态不正确，当前状态：{content.workflow_status}，只能审核已上传图片或待最终审核的文案'
            }), 400

        # 获取审核动作
        action = request.form.get('action')  # 'approve' 或 'reject'
        review_comment = request.form.get('review_comment', '')
        print(f"审核动作: {action}, 备注: {review_comment}")

        if action == 'approve':
            # 审核通过，根据客户设置决定下一步状态
            client = content.client
            if client and not client.need_review:
                # 客户设置为不需要审核，根据系统发布设置决定状态
                from app.models.system_setting import SystemSetting
                auto_publish_enabled = SystemSetting.get_value('auto_publish_enabled', 'false')

                if auto_publish_enabled.lower() in ['false', '0']:
                    # 关闭手动发布开关，自动发布，直接进入待发布状态
                    content.workflow_status = 'pending_publish'
                    content.publish_status = 'pending_publish'
                    content.client_review_status = 'auto_approved'
                    message = '最终审核通过，已自动提交发布'
                else:
                    # 开启手动发布开关，进入准备发布状态，需要手动提交
                    content.workflow_status = 'ready_to_publish'
                    content.client_review_status = 'auto_approved'
                    message = '最终审核通过，等待手动发布'
            else:
                # 需要客户审核
                content.workflow_status = 'pending_client_review'
                content.client_review_status = 'pending'
                message = '最终审核通过，等待客户审核'

            content.internal_review_status = 'approved'
            content.reviewer_id = current_user.id
            content.review_time = datetime.now()

        elif action == 'reject':
            # 获取驳回类型
            rejection_type = request.form.get('rejection_type', 'content')

            # 根据驳回类型决定流转状态
            if rejection_type == 'image':
                # 只有图片问题，回到图片上传阶段
                content.workflow_status = 'first_reviewed'
                content.internal_review_status = 'final_rej_img'  # 标记为最终审核驳回（图片问题）
                message = '最终审核驳回（图片问题），请重新上传图片'
            elif rejection_type == 'both':
                # 文案和图片都有问题，回到草稿状态
                content.workflow_status = 'draft'
                content.internal_review_status = 'final_rej_both'  # 标记为最终审核驳回（两者都有问题）
                # 重置完成状态标记
                content.content_completed = 0
                content.image_completed = 0
                message = '最终审核驳回（文案+图片问题），请重新编写文案和上传图片'
            else:
                # 文案问题，回到草稿状态
                content.workflow_status = 'draft'
                content.internal_review_status = 'final_rej_text'  # 标记为最终审核驳回（文案问题）
                message = '最终审核驳回（文案问题），请重新编写文案'

            content.reviewer_id = current_user.id
            content.review_time = datetime.now()

            # 记录驳回理由
            if review_comment:
                from app.models.content import RejectionReason
                rejection = RejectionReason(
                    content_id=content.id,
                    reason=review_comment,
                    rejection_type=rejection_type,
                    created_by=current_user.id,
                    created_at=datetime.now()
                )
                db.session.add(rejection)

        else:
            return jsonify({
                'success': False,
                'message': '无效的审核动作'
            }), 400

        db.session.commit()
        print(f"审核成功: {message}, 新状态: {content.workflow_status}")

        return jsonify({
            'success': True,
            'message': message,
            'new_status': content.workflow_status
        })

    except Exception as e:
        db.session.rollback()
        print(f"审核异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'审核失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/final-review/submit-reject', methods=['POST'])
@login_required
@csrf.exempt  # 跳过CSRF验证，避免400错误
def submit_reject_form():
    """处理驳回表单提交（非Ajax）"""
    try:
        from app.models.content import Content, RejectionReason
        from app import db

        # 获取表单数据
        content_id = request.form.get('content_id')
        rejection_type = request.form.get('rejection_type', 'content')
        reason = request.form.get('reason', '').strip()

        print(f"=== 表单数据调试 ===")
        print(f"content_id: {content_id}")
        print(f"rejection_type: {rejection_type}")
        print(f"reason: {reason}")
        print(f"所有表单数据: {dict(request.form)}")

        if not content_id or not reason:
            print(f"参数验证失败: content_id={content_id}, reason='{reason}'")
            flash('参数错误：缺少文案ID或驳回理由', 'error')
            return redirect('/simple/final-review')

        content_id = int(content_id)
        content = Content.query.get(content_id)

        if not content:
            flash('文案不存在', 'error')
            return redirect('/simple/final-review')

        print(f"=== 表单驳回提交，content_id: {content_id} ===")
        print(f"驳回类型: {rejection_type}, 理由: {reason}")

        # 根据驳回类型设置状态（使用更精确的状态标记）
        if rejection_type == 'content':
            # 只有文案问题
            content.workflow_status = 'draft'
            content.internal_review_status = 'final_rej_text'
            content.content_completed = 0
            content.image_completed = 1  # 图片没问题
        elif rejection_type == 'image':
            # 只有图片问题
            content.workflow_status = 'first_reviewed'
            content.internal_review_status = 'final_rej_img'
            content.content_completed = 1  # 文案没问题
            content.image_completed = 0
        else:  # both
            # 两者都有问题
            content.workflow_status = 'draft'
            content.internal_review_status = 'final_rej_both'
            content.content_completed = 0
            content.image_completed = 0

        content.status_update_time = datetime.now()
        content.updated_at = datetime.now()
        content.review_time = datetime.now()
        content.reviewer_id = current_user.id

        # 记录驳回理由
        rejection_reason = RejectionReason(
            content_id=content_id,
            reason=reason,
            created_at=datetime.now(),
            created_by=current_user.id,
            is_client=False,
            rejection_type=rejection_type
        )

        db.session.add(rejection_reason)
        db.session.commit()

        flash('驳回成功！', 'success')
        return redirect('/simple/final-review')

    except Exception as e:
        print(f"表单驳回失败: {str(e)}")
        flash(f'驳回失败：{str(e)}', 'error')
        return redirect('/simple/final-review')


@main_simple_bp.route('/api/final-review/batch', methods=['POST'])
@login_required
@csrf.exempt  # 跳过CSRF验证
def batch_final_review():
    """批量最终审核"""
    try:
        from app.models.content import Content, ContentHistory, RejectionReason
        from app import db

        # 获取参数
        content_ids_str = request.form.get('content_ids', '')
        action = request.form.get('action')  # 'approve' 或 'reject'
        review_comment = request.form.get('review_comment', '')

        if not content_ids_str:
            return jsonify({'success': False, 'message': '请选择要审核的文案'}), 400

        try:
            content_ids = [int(id.strip()) for id in content_ids_str.split(',') if id.strip()]
        except ValueError:
            return jsonify({'success': False, 'message': '文案ID格式错误'}), 400

        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要审核的文案'}), 400

        # 获取文案列表
        contents = Content.query.filter(Content.id.in_(content_ids)).all()

        success_count = 0
        error_messages = []

        for content in contents:
            try:
                # 检查状态
                if content.workflow_status != 'image_uploaded':
                    error_messages.append(f'文案"{content.title}"状态不正确')
                    continue

                if action == 'approve':
                    # 审核通过
                    client = content.client
                    if client and not client.need_review:
                        content.workflow_status = 'ready_to_publish'
                        content.client_review_status = 'auto_approved'
                    else:
                        content.workflow_status = 'pending_client_review'
                        content.client_review_status = 'pending'

                    content.internal_review_status = 'approved'

                elif action == 'reject':
                    # 审核驳回
                    content.workflow_status = 'pending_review'
                    content.internal_review_status = 'rejected'

                    # 记录驳回理由
                    if review_comment:
                        rejection = RejectionReason(
                            content_id=content.id,
                            reason=review_comment,
                            created_by=current_user.id,
                            created_at=datetime.now()
                        )
                        db.session.add(rejection)

                content.reviewer_id = current_user.id
                content.review_time = datetime.now()
                success_count += 1

            except Exception as e:
                error_messages.append(f'文案"{content.title}"处理失败：{str(e)}')

        db.session.commit()

        message = f'批量审核完成，成功处理 {success_count} 篇文案'
        if error_messages:
            message += f'，{len(error_messages)} 篇失败'

        return jsonify({
            'success': True,
            'message': message,
            'success_count': success_count,
            'error_count': len(error_messages),
            'errors': error_messages
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'批量审核失败：{str(e)}'
        }), 500


@main_simple_bp.route('/client-review')
@login_required
@menu_permission_required('/simple/client-review')
def client_review():
    """客户审核管理页面"""
    from app.models.client import Client, ClientShareLink
    from app.models.content import Content
    from app.utils.share_link import ShareLinkGenerator

    # 获取所有客户及其分享链接信息
    clients = Client.query.order_by(Client.name).all()

    client_data = []
    for client in clients:
        # 获取客户的活跃分享链接
        active_links = ShareLinkGenerator.get_client_share_links(client.id, include_inactive=False)

        # 获取客户的文案统计
        pending_count = Content.query.filter_by(
            client_id=client.id,
            workflow_status='pending_client_review',
            is_deleted=False
        ).count()

        reviewed_count = Content.query.filter(
            Content.client_id == client.id,
            Content.is_deleted == False,  # 修复：排除已删除的文章
            Content.client_review_status.in_(['approved', 'auto_approved', 'rejected'])
        ).count()

        # 修复：只统计真正进入过客户审核流程的文案
        # 包括：当前在客户审核中的、已被客户审核过的、曾被客户驳回的
        total_count = Content.query.filter(
            Content.client_id == client.id,
            Content.is_deleted == False,
            db.or_(
                # 当前在客户审核中
                Content.workflow_status == 'pending_client_review',
                # 已被客户审核过（通过、自动通过或驳回）
                Content.client_review_status.in_(['approved', 'auto_approved', 'rejected']),
                # 曾被客户驳回（通过内部审核状态标记）
                Content.internal_review_status.like('client_rej%')
            )
        ).count()

        client_data.append({
            'client': client,
            'active_links': active_links,
            'pending_count': pending_count,
            'reviewed_count': reviewed_count,
            'total_count': total_count,
            'has_active_link': len(active_links) > 0
        })

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，只返回页面内容
        return render_template('client_review/manage.html',
                             client_data=client_data)
    else:
        # 普通请求（包括刷新），返回完整页面
        return render_template('main_simple.html',
                             current_page='client-review',
                             page_content=render_template('client_review/manage.html',
                                                        client_data=client_data))


@main_simple_bp.route('/publish-manage')
@login_required
@menu_permission_required('/simple/publish-manage')
def publish_manage():
    """发布管理页面"""
    from app.models.content import Content
    from app.models.image import ContentImage
    from app.models.client import Client
    
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取筛选参数
    title_filter = request.args.get('title', '').strip()
    client_id_filter = request.args.get('client_id', '').strip()
    priority_filter = request.args.get('priority', '').strip()

    # 检查是否需要手动发布（统一逻辑：关闭 = 自动化）
    from app.models.system_setting import SystemSetting
    auto_publish_enabled = SystemSetting.get_value('auto_publish_enabled', 'false')

    if auto_publish_enabled.lower() in ['false', '0']:
        # 关闭手动发布开关，启用自动发布，发布管理页面不显示任何内容
        # 因为所有文案都会自动发布，不需要手动管理
        query = Content.query.filter(Content.id == -1)  # 永远不会匹配的条件，返回空结果
    else:
        # 开启手动发布开关，查询需要手动提交发布的内容
        query = Content.query.filter(
            Content.workflow_status.in_(['ready_to_publish', 'client_approved', 'pending_publish']),
            Content.is_deleted == False
        )

    # 应用筛选条件
    if title_filter:
        query = query.filter(Content.title.like(f'%{title_filter}%'))
    
    if client_id_filter:
        query = query.filter(Content.client_id == int(client_id_filter))
    
    if priority_filter:
        query = query.filter(Content.publish_priority == priority_filter)

    pagination = query.order_by(
        Content.publish_priority.desc(),
        Content.display_date.asc(),
        Content.display_time.asc(),
        Content.created_at.asc()
    ).paginate(page=page, per_page=per_page, error_out=False)

    # 组装内容和图片
    content_data = []
    for content in pagination.items:
        images = ContentImage.query.filter_by(content_id=content.id, is_deleted=False).order_by(ContentImage.image_order).all()
        content_data.append({
            'content': content,
            'images': images,
            'image_count': len(images)
        })

    # 获取所有客户列表用于筛选
    if auto_publish_enabled.lower() in ['false', '0']:
        # 自动发布模式下，没有需要手动管理的内容，返回空客户列表
        clients_with_content = []
    else:
        # 手动发布模式下，只获取有发布管理数据的客户
        clients_with_content = db.session.query(Client).join(Content).filter(
            Content.workflow_status.in_(['ready_to_publish', 'client_approved', 'pending_publish']),
            Content.is_deleted == False,
            Client.status == True
        ).distinct().order_by(Client.name).all()

        # 如果没有筛选到客户，则获取所有活跃客户
        if not clients_with_content:
            clients_with_content = Client.query.filter_by(status=True).order_by(Client.name).all()

    return render_template(
        'publish/manage.html',
        content_data=content_data,
        pagination=pagination,
        clients=clients_with_content
    )


@main_simple_bp.route('/system')
@login_required
@menu_permission_required('/simple/system')
def system():
    """系统设置页面"""
    from app.models.system_setting import SystemSetting

    # 获取所有系统设置
    settings = SystemSetting.get_all_settings()

    # 按类别分组设置
    workflow_settings = []
    upload_settings = []
    other_settings = []

    for setting in settings:
        if setting.key.startswith('ENABLE_'):
            workflow_settings.append(setting)
        elif setting.key.startswith('IMAGE_') or setting.key.startswith('MAX_'):
            upload_settings.append(setting)
        else:
            other_settings.append(setting)

    return render_template('system/settings.html',
                         workflow_settings=workflow_settings,
                         upload_settings=upload_settings,
                         other_settings=other_settings)


@main_simple_bp.route('/api/system/settings', methods=['GET'])
@login_required
def get_system_settings():
    """获取系统设置API"""
    try:
        from app.models.system_setting import SystemSetting

        settings = SystemSetting.get_all_settings()
        return jsonify({
            'success': True,
            'settings': [setting.to_dict() for setting in settings]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/settings', methods=['POST'])
@login_required
@csrf.exempt
def update_system_settings():
    """更新系统设置API"""
    try:
        from app.models.system_setting import SystemSetting
        from app import db

        # 获取要更新的设置
        settings_data = request.get_json()
        if not settings_data:
            return jsonify({
                'success': False,
                'message': '没有提供设置数据'
            }), 400

        updated_count = 0
        errors = []

        for key, value in settings_data.items():
            try:
                # 验证设置键名
                if not key or not isinstance(key, str):
                    errors.append(f'无效的设置键名：{key}')
                    continue

                # 更新设置
                SystemSetting.set_value(
                    key=key,
                    value=str(value),
                    updated_by=current_user.id
                )
                updated_count += 1

            except Exception as e:
                errors.append(f'更新设置 {key} 失败：{str(e)}')

        if errors:
            return jsonify({
                'success': False,
                'message': f'部分设置更新失败',
                'errors': errors,
                'updated_count': updated_count
            }), 400

        return jsonify({
            'success': True,
            'message': f'成功更新 {updated_count} 个设置',
            'updated_count': updated_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/settings/<setting_key>', methods=['PUT'])
@login_required
@csrf.exempt
def update_single_setting(setting_key):
    """更新单个系统设置API"""
    try:
        from app.models.system_setting import SystemSetting

        data = request.get_json()
        if not data or 'value' not in data:
            return jsonify({
                'success': False,
                'message': '缺少设置值'
            }), 400

        value = data['value']
        description = data.get('description')

        # 更新设置
        setting = SystemSetting.set_value(
            key=setting_key,
            value=str(value),
            description=description,
            updated_by=current_user.id
        )

        return jsonify({
            'success': True,
            'message': f'设置 {setting_key} 更新成功',
            'setting': setting.to_dict()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'更新设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/settings/reset', methods=['POST'])
@login_required
@csrf.exempt
def reset_system_settings():
    """重置系统设置为默认值API"""
    try:
        from app.models.system_setting import SystemSetting
        from app import db

        # 默认设置值
        default_settings = {
            'ENABLE_FIRST_REVIEW': '1',
            'ENABLE_FINAL_REVIEW': '1',
            'IMAGE_UPLOAD_MAX_SIZE': '10485760',
            'IMAGE_UPLOAD_ALLOWED_TYPES': 'jpg,jpeg,png,gif,webp',

            'MAX_IMAGES_PER_CONTENT': '9',
            'auto_publish_enabled': 'false',  # false = 自动发布，true = 手动发布
            'PUBLISH_TIMEOUT': '120',
            'PUBLISH_TIMEOUT_ACTION': 'keep_timeout',
            'PUBLISH_MAX_RETRY_COUNT': '3',
            'API_KEY': 'fETkRLwJQJkp29hzFIPuzaPWTcMcdWUW'
        }

        updated_count = 0
        for key, value in default_settings.items():
            SystemSetting.set_value(
                key=key,
                value=value,
                updated_by=current_user.id
            )
            updated_count += 1

        return jsonify({
            'success': True,
            'message': f'成功重置 {updated_count} 个设置为默认值',
            'updated_count': updated_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'重置设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/check-pending-final-review', methods=['GET'])
@login_required
@csrf.exempt
def check_pending_final_review():
    """检查待最终审核的文案数量"""
    try:
        from app.models.content import Content

        # 查询待最终审核的文案数量（使用与最终审核页面一致的状态值）
        pending_count = Content.query.filter(
            Content.workflow_status == 'final_review',
            Content.is_deleted == False
        ).count()

        return jsonify({
            'success': True,
            'pending_count': pending_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'检查待审核文案失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/process-pending-final-review', methods=['POST'])
@login_required
@csrf.exempt
def process_pending_final_review():
    """处理待最终审核的文案"""
    try:
        from app.models.content import Content
        from app.models.system_setting import SystemSetting
        from app import db

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '没有提供处理参数'
            }), 400

        action = data.get('action')
        setting_key = data.get('setting_key')
        setting_value = data.get('setting_value')

        if not action or not setting_key or setting_value is None:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        processed_count = 0

        if action == 'approve':
            # 自动通过所有待最终审核的文案（使用与最终审核页面一致的状态值）
            pending_contents = Content.query.filter(
                Content.workflow_status == 'final_review',
                Content.is_deleted == False
            ).all()

            for content in pending_contents:
                # 更新工作流状态为待客户审核
                content.workflow_status = 'pending_client_review'
                content.updated_at = datetime.now()
                processed_count += 1

            db.session.commit()

        # 保存系统设置
        SystemSetting.set_value(
            key=setting_key,
            value=setting_value,
            updated_by=current_user.id
        )

        return jsonify({
            'success': True,
            'message': f'成功处理 {processed_count} 篇文案',
            'processed_count': processed_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'处理失败：{str(e)}'
        }), 500


@main_simple_bp.route('/templates/add', methods=['GET', 'POST'])
@login_required
def add_template():
    """添加模板"""
    from app.models.template import Template, TemplateCategory, TemplateMark
    from app.forms.template import TemplateForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        form = TemplateForm()
        form.category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]

        if request.method == 'POST' and form.validate_on_submit():
            template = Template(
                title=form.title.data,
                content=form.content.data,
                category_id=form.category_id.data,
                creator_id=current_user.id,
                status=form.status.data
            )

            db.session.add(template)
            db.session.commit()

            return jsonify({'success': True, 'message': '模板添加成功'})

        # 获取所有标记
        marks = TemplateMark.query.all()

        return render_template('template/add_template_modal.html', form=form, marks=marks, title='添加模板')
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_template(id):
    """编辑模板"""
    from app.models.template import Template, TemplateCategory, TemplateMark
    from app.forms.template import TemplateForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        template = Template.query.get_or_404(id)
        form = TemplateForm(obj=template)
        form.category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]

        if request.method == 'POST' and form.validate_on_submit():
            template.title = form.title.data
            template.content = form.content.data
            template.category_id = form.category_id.data
            template.status = form.status.data
            template.updated_at = datetime.now()

            db.session.commit()

            return jsonify({'success': True, 'message': '模板更新成功'})

        # 获取所有标记
        marks = TemplateMark.query.all()

        return render_template('template/edit_template_form.html', form=form, marks=marks, title='编辑模板', template=template)
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/delete/<int:id>', methods=['POST'])
@login_required
def delete_template(id):
    """删除模板"""
    from app.models.template import Template
    from app.models.content import Content

    template = Template.query.get_or_404(id)

    # 检查是否有关联的文案
    if template.contents.count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该模板已被文案使用'})

    db.session.delete(template)
    db.session.commit()
    return jsonify({'success': True, 'message': '模板删除成功'})


@main_simple_bp.route('/templates/toggle_status/<int:id>', methods=['POST'])
@login_required
def toggle_template_status(id):
    """切换模板状态"""
    from app.models.template import Template

    template = Template.query.get_or_404(id)
    old_status = template.status
    template.status = not template.status
    template.updated_at = datetime.now()

    try:
        db.session.commit()
        status_text = '启用' if template.status else '禁用'

        print(f"DEBUG - 模板状态切换: ID={id}, 旧状态={old_status}, 新状态={template.status}")

        return jsonify({
            'success': True,
            'message': f'模板已{status_text}',
            'status': template.status  # 添加status字段
        })
    except Exception as e:
        db.session.rollback()
        print(f"ERROR - 切换模板状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'状态更新失败: {str(e)}'}), 500


@main_simple_bp.route('/templates/marks')
@login_required
def template_marks():
    """模板标记管理"""
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        from app.models.template import TemplateMark

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 限制每页显示数量的范围
        if per_page not in [10, 20, 30, 50, 80, 100]:
            per_page = 20

        # 分页查询所有标记
        try:
            pagination = TemplateMark.query.order_by(TemplateMark.id).paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
        except Exception as e:
            # 如果查询失败，返回空结果
            from flask_sqlalchemy import Pagination
            pagination = Pagination(query=None, page=1, per_page=per_page, total=0, items=[])

        return render_template('template/marks_modal.html',
                             marks=pagination.items,
                             pagination=pagination,
                             per_page=per_page)
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/category/add', methods=['GET', 'POST'])
@login_required
def add_template_category():
    """添加模板分类"""
    from app.models.template import TemplateCategory
    from app.forms.template import TemplateCategoryForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if request.method == 'POST':
            # 处理新的表单数据格式
            try:
                name = request.form.get('name', '').strip()
                parent_id = request.form.get('parent_id', '0')
                sort_order = request.form.get('sort_order', '0')

                if not name:
                    return jsonify({'success': False, 'message': '分类名称不能为空'})

                # 检查分类名称是否已存在
                existing = TemplateCategory.query.filter_by(name=name).first()
                if existing:
                    return jsonify({'success': False, 'message': '分类名称已存在'})

                category = TemplateCategory(
                    name=name,
                    parent_id=int(parent_id) if parent_id != '0' else None,
                    sort_order=int(sort_order) if sort_order else 0
                )

                db.session.add(category)
                db.session.commit()

                return jsonify({'success': True, 'message': '分类添加成功'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})
        else:
            # GET请求，返回表单模板
            form = TemplateCategoryForm()
            return render_template('template/category_form.html', form=form, title='添加分类')
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/category/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_template_category(id):
    """编辑模板分类"""
    from app.models.template import TemplateCategory
    from app.forms.template import TemplateCategoryForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        category = TemplateCategory.query.get_or_404(id)

        if request.method == 'POST':
            # 处理新的表单数据格式
            try:
                name = request.form.get('name', '').strip()
                parent_id = request.form.get('parent_id', '0')
                sort_order = request.form.get('sort_order', '0')

                if not name:
                    return jsonify({'success': False, 'message': '分类名称不能为空'})

                # 检查分类名称是否已存在（排除自己）
                existing = TemplateCategory.query.filter(
                    TemplateCategory.name == name,
                    TemplateCategory.id != id
                ).first()
                if existing:
                    return jsonify({'success': False, 'message': '分类名称已存在'})

                category.name = name
                category.parent_id = int(parent_id) if parent_id != '0' else None
                category.sort_order = int(sort_order) if sort_order else 0

                db.session.commit()

                return jsonify({'success': True, 'message': '分类修改成功'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'修改失败: {str(e)}'})
        else:
            # GET请求，返回表单模板
            form = TemplateCategoryForm(obj=category)
            return render_template('template/category_form.html', form=form, title='编辑分类', category=category)
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/categories/data', methods=['GET'])
@login_required
def get_categories_data():
    """获取分类数据（API）"""
    from app.models.template import TemplateCategory

    try:
        categories = TemplateCategory.query.all()
        categories_data = []
        for category in categories:
            categories_data.append({
                'id': category.id,
                'name': category.name,
                'parent_id': category.parent_id,
                'sort_order': category.sort_order or 0
            })

        return jsonify({
            'success': True,
            'categories': categories_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分类数据失败: {str(e)}'
        })


@main_simple_bp.route('/templates/category/<int:id>/data', methods=['GET'])
@login_required
def get_category_data(id):
    """获取单个分类数据（API）"""
    from app.models.template import TemplateCategory

    try:
        category = TemplateCategory.query.get_or_404(id)

        return jsonify({
            'success': True,
            'category': {
                'id': category.id,
                'name': category.name,
                'parent_id': category.parent_id,
                'sort_order': category.sort_order or 0
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分类数据失败: {str(e)}'
        })


@main_simple_bp.route('/templates/category/delete/<int:id>', methods=['POST'])
@login_required
def delete_template_category(id):
    """删除模板分类"""
    from app.models.template import TemplateCategory, Template

    category = TemplateCategory.query.get_or_404(id)

    # 检查是否有关联的模板
    if Template.query.filter_by(category_id=id).count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该分类下还有模板'})

    # 检查是否有子分类
    if TemplateCategory.query.filter_by(parent_id=id).count() > 0:
        return jsonify({'success': False, 'message': '无法删除，该分类下还有子分类'})

    db.session.delete(category)
    db.session.commit()
    return jsonify({'success': True, 'message': '分类删除成功'})


@main_simple_bp.route('/templates/mark/add', methods=['GET', 'POST'])
@login_required
def add_template_mark():
    """添加模板标记"""
    from app.models.template import TemplateMark
    from app.forms.template import TemplateMarkForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if request.method == 'POST':
            # 处理表单数据格式
            try:
                name = request.form.get('name', '').strip()
                description = request.form.get('description', '').strip()

                if not name:
                    return jsonify({'success': False, 'message': '标记名称不能为空'})

                # 检查标记名称是否已存在
                existing = TemplateMark.query.filter_by(name=name).first()
                if existing:
                    return jsonify({'success': False, 'message': '标记名称已存在'})

                mark = TemplateMark(
                    name=name,
                    description=description
                )

                db.session.add(mark)
                db.session.commit()

                return jsonify({'success': True, 'message': '标记添加成功'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})
        else:
            # GET请求，返回表单模板
            form = TemplateMarkForm()
            return render_template('template/mark_form.html', form=form, title='添加标记')
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/mark/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_template_mark(id):
    """编辑模板标记"""
    from app.models.template import TemplateMark
    from app.forms.template import TemplateMarkForm

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        mark = TemplateMark.query.get_or_404(id)

        if request.method == 'POST':
            # 处理表单数据格式
            try:
                name = request.form.get('name', '').strip()
                description = request.form.get('description', '').strip()

                if not name:
                    return jsonify({'success': False, 'message': '标记名称不能为空'})

                # 检查标记名称是否已存在（排除自己）
                existing = TemplateMark.query.filter(
                    TemplateMark.name == name,
                    TemplateMark.id != id
                ).first()
                if existing:
                    return jsonify({'success': False, 'message': '标记名称已存在'})

                mark.name = name
                mark.description = description

                db.session.commit()

                return jsonify({'success': True, 'message': '标记修改成功'})
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': False, 'message': f'修改失败: {str(e)}'})
        else:
            # GET请求，返回表单模板
            form = TemplateMarkForm(obj=mark)
            return render_template('template/mark_form.html', form=form, title='编辑标记', mark=mark)
    else:
        return render_template('main_simple.html')


@main_simple_bp.route('/templates/mark/<int:id>/data', methods=['GET'])
@login_required
def get_mark_data(id):
    """获取单个标记数据（API）"""
    from app.models.template import TemplateMark

    try:
        mark = TemplateMark.query.get_or_404(id)

        return jsonify({
            'success': True,
            'mark': {
                'id': mark.id,
                'name': mark.name,
                'description': mark.description or ''
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取标记数据失败: {str(e)}'
        })


@main_simple_bp.route('/templates/mark/delete/<int:id>', methods=['POST'])
@login_required
def delete_template_mark(id):
    """删除模板标记"""
    from app.models.template import TemplateMark

    mark = TemplateMark.query.get_or_404(id)

    db.session.delete(mark)
    db.session.commit()
    return jsonify({'success': True, 'message': '标记删除成功'})


@main_simple_bp.route('/templates/categories')
@login_required
def template_categories():
    """模板分类管理"""
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        from app.models.template import TemplateCategory

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 限制每页显示数量的范围
        if per_page not in [10, 20, 30, 50, 80, 100]:
            per_page = 20

        # 分页查询所有分类
        pagination = TemplateCategory.query.order_by(TemplateCategory.id).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return render_template('template/categories_modal.html',
                             categories=pagination.items,
                             pagination=pagination,
                             per_page=per_page)
    else:
        return render_template('main_simple.html')


# === 客户分享链接管理API ===

@main_simple_bp.route('/api/clients/<int:client_id>/share-links', methods=['GET'])
@login_required
def get_client_share_links(client_id):
    """获取客户的分享链接列表"""
    try:
        from app.utils.share_link import ShareLinkGenerator

        links = ShareLinkGenerator.get_client_share_links(client_id, include_inactive=False)

        return jsonify({
            'success': True,
            'links': [link.to_dict() for link in links]
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/share-link-defaults', methods=['GET'])
@login_required
def get_share_link_defaults():
    """获取分享链接的默认设置"""
    try:
        from app.models.system_setting import SystemSetting

        # 使用默认有效期（永久有效）
        expires_days = 0  # 0表示永久有效

        return jsonify({
            'success': True,
            'defaults': {
                'expires_days': expires_days,
                'expires_label': '永久有效' if expires_days == 0 else f'{expires_days}天'
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取默认设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/system/image-limits', methods=['GET'])
@login_required
def get_image_limits():
    """获取图片上传限制设置"""
    try:
        from app.models.system_setting import SystemSetting

        # 获取图片数量限制
        max_images = int(SystemSetting.get_value('MAX_IMAGES_PER_CONTENT', '9'))

        # 获取图片大小限制
        max_size = int(SystemSetting.get_value('IMAGE_UPLOAD_MAX_SIZE', '10485760'))
        max_size_mb = round(max_size / (1024 * 1024), 1)

        # 获取允许的图片类型
        allowed_types = SystemSetting.get_value('IMAGE_UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,webp')

        return jsonify({
            'success': True,
            'limits': {
                'max_images': max_images,
                'max_size': max_size,
                'max_size_mb': max_size_mb,
                'allowed_types': allowed_types.split(',')
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取图片限制设置失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/share-links', methods=['POST'])
@login_required
@csrf.exempt
def create_client_share_link(client_id):
    """为客户创建分享链接"""
    try:
        from app.utils.share_link import ShareLinkGenerator
        from app.models.client import Client
        from app.models.task import Task

        # 验证客户是否存在
        client = Client.query.get_or_404(client_id)

        # 获取参数
        expires_days = request.form.get('expires_days', type=int)
        if expires_days is None:
            # 使用默认值（永久有效）
            expires_days = 0  # 0表示永久有效
        access_key = request.form.get('access_key', '').strip()
        task_id = request.form.get('task_id', type=int)

        # 验证访问密钥
        if not access_key or len(access_key) != 4:
            return jsonify({
                'success': False,
                'message': '访问密钥必须是4位字符'
            }), 400

        # 验证任务是否存在（如果指定了任务）
        task_name = None
        if task_id:
            task = Task.query.filter_by(id=task_id, client_id=client_id).first()
            if not task:
                return jsonify({
                    'success': False,
                    'message': '指定的任务不存在'
                }), 400
            task_name = task.name

        # 创建分享链接
        share_link = ShareLinkGenerator.create_share_link(
            client_id=client_id,
            expires_days=expires_days if expires_days > 0 else None,
            access_key=access_key,
            task_id=task_id
        )

        # 构建返回信息
        link_info = {
            'id': share_link.id,
            'share_url': share_link.share_url,
            'access_key': access_key,
            'expires_at': share_link.expires_at.strftime('%Y-%m-%d %H:%M:%S') if share_link.expires_at else None,
            'created_at': share_link.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'task_name': task_name,
            'client_name': client.name
        }

        # 暂时将访问密钥和任务信息存储在分享链接的URL参数中
        # 等数据库迁移完成后可以存储在数据库中
        if access_key:
            link_info['share_url'] += f'?key={access_key}'
        if task_id:
            link_info['share_url'] += f'&task={task_id}'

        return jsonify({
            'success': True,
            'message': '分享链接创建成功',
            'link_info': link_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'创建分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/tasks', methods=['GET'])
@login_required
def get_client_tasks_for_share(client_id):
    """获取客户的任务列表"""
    try:
        from app.models.client import Client
        from app.models.task import Task
        from app.models.content import Content

        # 验证客户是否存在
        client = Client.query.get_or_404(client_id)

        # 获取客户的任务列表
        tasks = Task.query.filter_by(client_id=client_id).order_by(Task.created_at.desc()).all()

        # 统计每个任务的文案数量
        task_list = []
        for task in tasks:
            content_count = Content.query.filter_by(task_id=task.id).count()
            task_list.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'content_count': content_count,
                'status': task.status,
                'created_at': task.created_at.strftime('%Y-%m-%d')
            })

        return jsonify({
            'success': True,
            'tasks': task_list
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务列表失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/share-links/all', methods=['GET'])
@login_required
def get_all_client_share_links(client_id):
    """获取客户的所有分享链接"""
    try:
        from app.models.client import Client, ClientShareLink
        from app.models.task import Task

        # 验证客户是否存在
        client = Client.query.get_or_404(client_id)

        # 获取客户的所有活跃分享链接
        share_links = ClientShareLink.query.filter_by(
            client_id=client_id,
            is_active=True
        ).order_by(ClientShareLink.created_at.desc()).all()

        # 构建返回数据
        links_data = []
        for link in share_links:
            link_dict = link.to_dict()
            # 添加完整的分享URL
            link_dict['share_url'] = link.share_url
            if link.access_key:
                link_dict['share_url'] += f'?key={link.access_key}'
            if link.task_id:
                link_dict['share_url'] += f'&task={link.task_id}'
            links_data.append(link_dict)

        return jsonify({
            'success': True,
            'links': links_data,
            'client_name': client.name
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/share-links/<share_key>/refresh', methods=['POST'])
@login_required
@csrf.exempt
def refresh_single_share_link(share_key):
    """刷新单个分享链接"""
    try:
        from app.models.client import ClientShareLink
        from app.utils.share_link import ShareLinkGenerator

        # 查找分享链接
        share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
        if not share_link:
            return jsonify({
                'success': False,
                'message': '分享链接不存在'
            }), 404

        # 禁用旧链接
        share_link.is_active = False

        # 创建新链接（保持相同的配置）
        new_share_link = ShareLinkGenerator.create_share_link(
            client_id=share_link.client_id,
            expires_days=None if not share_link.expires_at else
                        (share_link.expires_at - share_link.created_at).days,
            access_key=share_link.access_key,
            task_id=share_link.task_id
        )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '分享链接已刷新',
            'new_share_key': new_share_link.share_key
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/share-links/<share_key>/disable', methods=['POST'])
@login_required
@csrf.exempt
def disable_share_link(share_key):
    """删除分享链接"""
    try:
        from app.models.client import ClientShareLink

        # 查找分享链接
        share_link = ClientShareLink.query.filter_by(share_key=share_key).first()
        if not share_link:
            return jsonify({
                'success': False,
                'message': '分享链接不存在'
            }), 404

        # 直接删除链接记录
        db.session.delete(share_link)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '分享链接已删除'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'删除分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/clients/<int:client_id>/share-links/refresh', methods=['POST'])
@login_required
def refresh_client_share_link(client_id):
    """刷新客户的分享链接"""
    try:
        from app.utils.share_link import ShareLinkGenerator
        from app.models.client import Client

        # 验证客户是否存在
        client = Client.query.get_or_404(client_id)

        # 获取有效期参数
        expires_days = request.form.get('expires_days', type=int)

        # 刷新分享链接
        new_link = ShareLinkGenerator.refresh_share_link(client_id, expires_days)

        return jsonify({
            'success': True,
            'message': '分享链接刷新成功',
            'link': new_link.to_dict(),
            'share_url': new_link.share_url
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'刷新分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/share-links/<share_key>/deactivate', methods=['POST'])
@login_required
def deactivate_share_link(share_key):
    """禁用分享链接"""
    try:
        from app.utils.share_link import ShareLinkGenerator

        success = ShareLinkGenerator.deactivate_share_link(share_key)

        if success:
            return jsonify({
                'success': True,
                'message': '分享链接已禁用'
            })
        else:
            return jsonify({
                'success': False,
                'message': '分享链接不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'禁用分享链接失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/share-links/<share_key>/stats', methods=['GET'])
@login_required
def get_share_link_stats(share_key):
    """获取分享链接统计信息"""
    try:
        from app.utils.share_link import ShareLinkGenerator

        stats = ShareLinkGenerator.get_share_link_stats(share_key)

        if stats:
            return jsonify({
                'success': True,
                'stats': {
                    'pending_count': stats['pending_count'],
                    'reviewed_count': stats['reviewed_count'],
                    'total_count': stats['total_count'],
                    'is_expired': stats['is_expired'],
                    'days_remaining': stats['days_remaining']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '分享链接不存在'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计信息失败：{str(e)}'
        }), 500


# === 发布管理API ===

@main_simple_bp.route('/api/publish/<int:content_id>', methods=['POST'])
@login_required
def publish_content(content_id):
    """发布单个文案"""
    try:
        content = Content.query.get_or_404(content_id)
        
        # 检查权限
        if not current_user.has_permission('publish.manage'):
            return jsonify({'success': False, 'message': '无权限执行此操作'})
        
        # 更新状态为已发布
        content.workflow_status = 'published'
        content.published_at = datetime.now()
        content.published_by = current_user.id
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '发布成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'发布失败: {str(e)}'})


@main_simple_bp.route('/api/publish/<int:content_id>/priority', methods=['POST'])
@login_required
def set_content_priority(content_id):
    """设置文案发布优先级"""
    try:
        content = Content.query.get_or_404(content_id)
        
        # 检查权限
        if not current_user.has_permission('publish.manage'):
            return jsonify({'success': False, 'message': '无权限执行此操作'})
        
        data = request.get_json()
        priority = data.get('priority')
        
        if priority not in ['high', 'normal', 'low']:
            return jsonify({'success': False, 'message': '优先级值无效'})
        
        content.publish_priority = priority
        db.session.commit()
        
        return jsonify({'success': True, 'message': '优先级设置成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'设置失败: {str(e)}'})


@main_simple_bp.route('/api/publish/batch', methods=['POST'])
@login_required
def batch_publish():
    """批量发布文案"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要发布的文案'})
        
        # 检查权限
        if not current_user.has_permission('publish.manage'):
            return jsonify({'success': False, 'message': '无权限执行此操作'})
        
        contents = Content.query.filter(Content.id.in_(content_ids)).all()
        published_count = 0
        
        for content in contents:
            if content.workflow_status in ['ready_to_publish', 'client_approved', 'pending_publish']:
                content.workflow_status = 'published'
                content.published_at = datetime.now()
                content.published_by = current_user.id
                published_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'批量发布成功，共发布 {published_count} 篇文案'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量发布失败: {str(e)}'})


@main_simple_bp.route('/api/publish/batch-priority', methods=['POST'])
@login_required
def batch_set_priority():
    """批量设置优先级"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        priority = data.get('priority')
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要设置的文案'})
        
        if priority not in ['high', 'normal', 'low']:
            return jsonify({'success': False, 'message': '优先级值无效'})
        
        # 检查权限
        if not current_user.has_permission('publish.manage'):
            return jsonify({'success': False, 'message': '无权限执行此操作'})
        
        contents = Content.query.filter(Content.id.in_(content_ids)).all()
        
        for content in contents:
            content.publish_priority = priority
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'批量设置优先级成功，共设置 {len(contents)} 篇文案'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量设置失败: {str(e)}'})


@main_simple_bp.route('/api/contents/<int:content_id>/approve', methods=['POST'])
@login_required
@csrf.exempt
def approve_content_api(content_id):
    """通过审核API"""
    try:
        from app.models.content import Content
        from datetime import datetime

        content = Content.query.get_or_404(content_id)

        # 根据当前状态决定下一步状态
        if content.workflow_status == 'final_review':
            # 最终审核通过
            content.workflow_status = 'ready_to_publish'
            content.internal_review_status = 'approved'
            message = '最终审核通过'
        elif content.workflow_status == 'pending_review':
            # 初审通过
            content.workflow_status = 'first_approved'
            content.internal_review_status = 'approved'
            message = '初审通过'
        else:
            return jsonify({
                'success': False,
                'message': f'当前状态({content.workflow_status})不允许审核'
            }), 400

        # 记录审核信息
        content.reviewer_id = current_user.id
        content.review_time = datetime.now()
        content.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'审核失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/contents/<int:content_id>/reject', methods=['POST'])
@login_required
@csrf.exempt
def reject_content_api(content_id):
    """驳回审核API"""
    try:
        print(f"=== 驳回API被调用，content_id: {content_id} ===")
        print(f"请求方法: {request.method}")
        print(f"请求头: {dict(request.headers)}")
        print(f"请求数据: {request.get_data()}")

        from app.models.content import Content
        from app.models.content import RejectionReason
        from datetime import datetime
        import json

        content = Content.query.get_or_404(content_id)
        print(f"找到文案: {content.id}, 当前状态: {content.workflow_status}")

        # 获取驳回理由
        data = request.get_json() or {}
        print(f"解析的JSON数据: {data}")
        reason = data.get('reason', '').strip()
        print(f"驳回理由: '{reason}'")

        if not reason:
            print("驳回理由为空，返回400错误")
            return jsonify({
                'success': False,
                'message': '请输入驳回理由'
            }), 400

        # 根据当前状态决定回退状态
        if content.workflow_status == 'final_review':
            # 最终审核驳回，回到初审通过状态
            content.workflow_status = 'first_approved'
            message = '最终审核驳回'
        elif content.workflow_status == 'pending_review':
            # 初审驳回，回到草稿状态
            content.workflow_status = 'draft'
            message = '初审驳回'
        else:
            return jsonify({
                'success': False,
                'message': f'当前状态({content.workflow_status})不允许驳回'
            }), 400

        # 记录驳回理由
        rejection = RejectionReason(
            content_id=content.id,
            reason=reason,
            created_by=current_user.id,
            created_at=datetime.now()
        )
        db.session.add(rejection)

        # 更新审核信息
        content.internal_review_status = 'rejected'
        content.reviewer_id = current_user.id
        content.review_time = datetime.now()
        content.updated_at = datetime.now()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'驳回失败：{str(e)}'
        }), 500


@main_simple_bp.route('/api/contents/<int:content_id>')
@login_required
def get_content_api(content_id):
    """获取单个文案的详细信息"""
    try:
        content = Content.query.get_or_404(content_id)

        # 检查权限
        if not current_user.is_admin and content.user_id != current_user.id:
            return jsonify({'success': False, 'message': '无权限访问此文案'})

        # 构建返回数据
        content_data = {
            'id': content.id,
            'title': content.title,
            'content': content.content,
            'topics': content.topics,
            'at_users': content.at_users,
            'location': content.location,
            'workflow_status': content.workflow_status,
            'internal_review_status': content.internal_review_status,
            'client_review_status': content.client_review_status,
            'client_name': content.client.name if content.client else None,
            'created_at': content.created_at.strftime('%Y-%m-%d %H:%M') if content.created_at else None
        }

        return jsonify({
            'success': True,
            'content': content_data
        })

    except Exception as e:
        print(f"获取文案信息失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取文案信息失败'})


@main_simple_bp.route('/api/rejection-reasons/<int:content_id>')
@login_required
def get_rejection_reasons_api(content_id):
    """获取文案的驳回理由"""
    try:
        content = Content.query.get_or_404(content_id)

        # 检查权限
        if not current_user.is_admin and content.user_id != current_user.id:
            return jsonify({'success': False, 'message': '无权限访问此文案'})

        # 获取驳回理由，按创建时间倒序
        from app.models.content import RejectionReason
        reasons = RejectionReason.query.filter_by(content_id=content_id)\
                                     .order_by(RejectionReason.created_at.desc())\
                                     .all()

        # 构建返回数据
        reasons_data = []
        for reason in reasons:
            reason_data = {
                'id': reason.id,
                'reason': reason.reason,
                'rejection_type': reason.rejection_type if hasattr(reason, 'rejection_type') else 'content',
                'is_client': reason.is_client,
                'created_at': reason.created_at.strftime('%Y-%m-%d %H:%M:%S') if reason.created_at else None,
                'creator_name': reason.creator.username if reason.creator else '客户'
            }
            reasons_data.append(reason_data)

        return jsonify({
            'success': True,
            'reasons': reasons_data
        })

    except Exception as e:
        print(f"获取驳回理由失败: {str(e)}")
        return jsonify({'success': False, 'message': '获取驳回理由失败'})


@main_simple_bp.route('/api/debug/content-status/<int:content_id>')
@login_required
def debug_content_status(content_id):
    """调试文案状态信息"""
    try:
        content = Content.query.get_or_404(content_id)

        # 获取驳回理由
        from app.models.content import RejectionReason
        reasons = RejectionReason.query.filter_by(content_id=content_id).all()

        debug_info = {
            'content_id': content.id,
            'title': content.title,
            'workflow_status': content.workflow_status,
            'internal_review_status': content.internal_review_status,
            'client_review_status': content.client_review_status,
            'rejection_reasons_count': len(reasons),
            'rejection_reasons': []
        }

        for reason in reasons:
            reason_info = {
                'id': reason.id,
                'reason': reason.reason,
                'is_client': reason.is_client,
                'created_at': reason.created_at.strftime('%Y-%m-%d %H:%M:%S') if reason.created_at else None,
                'has_rejection_type': hasattr(reason, 'rejection_type'),
                'rejection_type': getattr(reason, 'rejection_type', 'N/A')
            }
            debug_info['rejection_reasons'].append(reason_info)

        return jsonify({
            'success': True,
            'debug_info': debug_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@main_simple_bp.route('/publish-status-manage')
@login_required
@menu_permission_required('/simple/publish-status-manage')
def publish_status_manage():
    """发布状态管理页面"""
    from app.models.content import Content
    from app.models.image import ContentImage
    from app.models.client import Client

    page = request.args.get('page', 1, type=int)
    per_page = 20
    status = request.args.get('status', 'pending')  # 默认显示待发布状态
    client_id = request.args.get('client_id', type=int)
    date_filter = request.args.get('date_filter')
    priority = request.args.get('priority')

    # 支持的状态映射
    status_map = {
        'pending': ['pending_publish'],
        'publishing': ['publishing'],
        'published': ['published'],
        'failed': ['publish_failed'],
        'timeout': ['publish_timeout'],
        'all': None  # 显示所有状态
    }

    # 构建查询
    query = Content.query.filter(Content.is_deleted == False)

    # 状态筛选
    if status in status_map:
        if status_map[status] is not None:
            # 筛选特定状态
            query = query.filter(Content.workflow_status.in_(status_map[status]))
        else:
            # status='all'，显示所有已进入发布流程的内容
            query = query.filter(Content.workflow_status.in_([
                'pending_publish', 'publishing', 'published', 'publish_failed', 'publish_timeout'
            ]))
    else:
        # 未知状态，显示所有已进入发布流程的内容
        query = query.filter(Content.workflow_status.in_([
            'pending_publish', 'publishing', 'published', 'publish_failed', 'publish_timeout'
        ]))

    # 客户筛选
    if client_id:
        query = query.filter(Content.client_id == client_id)

    # 日期筛选
    if date_filter:
        from datetime import date, timedelta
        today = date.today()

        if date_filter == 'today':
            query = query.filter(Content.display_date == today)
        elif date_filter == 'yesterday':
            yesterday = today - timedelta(days=1)
            query = query.filter(Content.display_date == yesterday)
        elif date_filter == 'this_week':
            # 本周（周一到周日）
            days_since_monday = today.weekday()
            monday = today - timedelta(days=days_since_monday)
            sunday = monday + timedelta(days=6)
            query = query.filter(Content.display_date >= monday, Content.display_date <= sunday)
        elif date_filter == 'last_week':
            # 上周（上周一到上周日）
            days_since_monday = today.weekday()
            this_monday = today - timedelta(days=days_since_monday)
            last_monday = this_monday - timedelta(days=7)
            last_sunday = last_monday + timedelta(days=6)
            query = query.filter(Content.display_date >= last_monday, Content.display_date <= last_sunday)

    # 优先级筛选
    if priority:
        query = query.filter(Content.publish_priority == priority)

    # 获取所有有内容的客户列表（用于筛选下拉框）
    clients = Client.query.join(Content).filter(
        Content.is_deleted == False,
        Content.workflow_status.in_([
            'pending_publish', 'publishing', 'published', 'publish_failed', 'publish_timeout'
        ])
    ).distinct().all()

    pagination = query.order_by(
        Content.publish_priority.desc(),
        Content.display_date.asc(),
        Content.display_time.asc(),
        Content.created_at.asc()
    ).paginate(page=page, per_page=per_page, error_out=False)

    content_data = []
    for content in pagination.items:
        images = ContentImage.query.filter_by(content_id=content.id, is_deleted=False).order_by(ContentImage.image_order).all()

        # 获取最新的发布记录（用于显示提示信息）
        from app.models.publish import PublishRecord
        latest_publish_record = PublishRecord.query.filter_by(content_id=content.id).order_by(PublishRecord.publish_time.desc()).first()

        content_data.append({
            'content': content,
            'images': images,
            'image_count': len(images),
            'latest_publish_record': latest_publish_record,
            'images_json': json.dumps([{
                'url': f"/static/uploads/{img.image_path}" if img.image_path and not img.image_path.startswith(('http', '/static')) else img.image_path,
                'order': img.image_order,
                'id': img.id
            } for img in images])
        })

    return render_template(
        'publish/status_manage.html',
        content_data=content_data,
        pagination=pagination,
        current_status=status,
        clients=clients
    )


@main_simple_bp.route('/api/content-detail/<int:content_id>')
@login_required
def get_publish_content_detail(content_id):
    """获取文案详情API"""
    try:
        from app.models.content import Content
        from app.models.image import ContentImage
        from app.models.publish import PublishRecord
        from app.models.client import Client

        # 获取文案信息
        content = Content.query.get(content_id)
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在'
            }), 404

        # 获取关联图片
        images = ContentImage.query.filter_by(
            content_id=content_id,
            is_deleted=False
        ).order_by(ContentImage.image_order).all()

        # 获取发布记录
        publish_records = PublishRecord.query.filter_by(
            content_id=content_id
        ).order_by(PublishRecord.publish_time.desc()).all()

        # 构建返回数据
        content_data = {
            'id': content.id,
            'title': content.title,
            'content': content.content,
            'client_name': content.client.name if content.client else None,
            'priority': content.publish_priority,
            'workflow_status': content.workflow_status,
            'publish_status': content.publish_status,
            'created_at': content.created_at.strftime('%Y-%m-%d %H:%M:%S') if content.created_at else None,
            'publish_time': content.publish_time.strftime('%Y-%m-%d %H:%M:%S') if content.publish_time else None,
            'status_update_time': content.status_update_time.strftime('%Y-%m-%d %H:%M:%S') if content.status_update_time else None,
            'images': [
                {
                    'url': image.image_url,
                    'order': image.image_order
                } for image in images
            ],
            'publish_records': [
                {
                    'publish_time': record.publish_time.strftime('%Y-%m-%d %H:%M:%S') if record.publish_time else None,
                    'status': record.status,
                    'platform': record.platform,
                    'account': record.account,
                    'publish_url': record.publish_url,
                    'error_message': record.error_message
                } for record in publish_records
            ]
        }

        return jsonify({
            'success': True,
            'content': content_data
        })

    except Exception as e:
        print(f"获取文案详情失败: {e}")
        return jsonify({
            'success': False,
            'message': '获取详情失败，请重试'
        }), 500


@main_simple_bp.route('/api/update-priority', methods=['POST'])
@login_required
def update_priority():
    """更新单个文案优先级"""
    try:
        data = request.get_json()
        content_id = data.get('content_id')
        priority = data.get('priority')

        if not content_id or not priority:
            return jsonify({
                'success': False,
                'message': '参数不完整'
            }), 400

        if priority not in ['high', 'normal', 'low']:
            return jsonify({
                'success': False,
                'message': '无效的优先级值'
            }), 400

        from app.models.content import Content
        content = Content.query.get(content_id)
        if not content:
            return jsonify({
                'success': False,
                'message': '文案不存在'
            }), 404

        content.publish_priority = priority
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '优先级更新成功'
        })

    except Exception as e:
        db.session.rollback()
        print(f"更新优先级失败: {e}")
        return jsonify({
            'success': False,
            'message': '更新失败，请重试'
        }), 500


@main_simple_bp.route('/api/batch-update-priority', methods=['POST'])
@login_required
def batch_update_priority():
    """批量更新文案优先级"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        priority = data.get('priority')

        if not content_ids or not priority:
            return jsonify({
                'success': False,
                'message': '参数不完整'
            }), 400

        if priority not in ['high', 'normal', 'low']:
            return jsonify({
                'success': False,
                'message': '无效的优先级值'
            }), 400

        from app.models.content import Content

        # 批量更新
        updated_count = Content.query.filter(
            Content.id.in_(content_ids),
            Content.is_deleted == False
        ).update(
            {Content.publish_priority: priority},
            synchronize_session=False
        )

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功更新 {updated_count} 篇文案的优先级',
            'updated_count': updated_count
        })

    except Exception as e:
        db.session.rollback()
        print(f"批量更新优先级失败: {e}")
        return jsonify({
            'success': False,
            'message': '批量更新失败，请重试'
        }), 500


@main_simple_bp.route('/api/submit-for-publish/<int:content_id>', methods=['POST'])
@login_required
def submit_for_publish(content_id):
    """提交文案进行发布（进入发布队列）"""
    try:
        content = Content.query.get_or_404(content_id)
        
        # 检查权限
        if not current_user.has_permission('publish_manage'):
            return jsonify({'success': False, 'message': '无权限执行此操作'})
        
        # 检查文案状态是否允许提交发布
        if content.workflow_status not in ['ready_to_publish', 'client_approved', 'pending_publish']:
            return jsonify({'success': False, 'message': '文案状态不允许提交发布'})
        
        # 更新状态为待发布（等待第三方API调用）
        content.workflow_status = 'pending_publish'
        content.publish_status = 'pending_publish'
        content.status_update_time = datetime.now()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '提交发布成功，文案已进入发布队列'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'提交发布失败: {str(e)}'})


@main_simple_bp.route('/api/batch-submit-for-publish', methods=['POST'])
@login_required
def batch_submit_for_publish():
    """批量提交发布"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要提交发布的文案'})
        
        submitted_count = 0
        for content_id in content_ids:
            content = Content.query.get(content_id)
            if content and content.workflow_status in ['ready_to_publish', 'client_approved', 'pending_publish']:
                content.workflow_status = 'pending_publish'
                content.publish_status = 'pending_publish'
                content.status_update_time = datetime.now()
                submitted_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'批量提交发布成功！{submitted_count} 篇文案已进入发布队列。',
            'submitted_count': submitted_count
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量提交发布失败：{str(e)}'})

@main_simple_bp.route('/api/publish-manage/delete/<int:content_id>', methods=['POST'])
@login_required
def delete_publish_content(content_id):
    """删除发布管理中的文案"""
    try:
        content = Content.query.get(content_id)
        if not content:
            return jsonify({'success': False, 'message': '文案不存在'})
        
        # 检查文案状态是否允许删除
        if content.workflow_status not in ['ready_to_publish', 'client_approved', 'pending_publish']:
            return jsonify({'success': False, 'message': '只能删除待发布状态的文案'})
        
        # 删除关联的图片文件
        from app.models.image import ContentImage
        import os
        
        images = ContentImage.query.filter_by(content_id=content_id, is_deleted=False).all()
        deleted_files = []
        
        for image in images:
            # 删除原图文件
            if image.image_path:
                image_full_path = os.path.join(current_app.root_path, 'static', image.image_path.lstrip('/'))
                if os.path.exists(image_full_path):
                    try:
                        os.remove(image_full_path)
                        deleted_files.append(image.image_path)
                    except Exception as e:
                        print(f"删除原图文件失败: {image_full_path}, 错误: {e}")
            
            # 删除缩略图文件
            if image.thumbnail_path:
                thumbnail_full_path = os.path.join(current_app.root_path, 'static', image.thumbnail_path.lstrip('/'))
                if os.path.exists(thumbnail_full_path):
                    try:
                        os.remove(thumbnail_full_path)
                        deleted_files.append(image.thumbnail_path)
                    except Exception as e:
                        print(f"删除缩略图文件失败: {thumbnail_full_path}, 错误: {e}")
            
            # 标记图片为已删除
            image.is_deleted = True
            image.deleted_at = datetime.now()
        
        # 标记文案为已删除
        content.is_deleted = True
        content.deleted_at = datetime.now()
        content.deleted_by = current_user.id
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'文案删除成功！已删除 {len(images)} 张图片文件。',
            'deleted_images_count': len(images),
            'deleted_files': deleted_files
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败：{str(e)}'})

@main_simple_bp.route('/api/publish-manage/batch-delete', methods=['POST'])
@login_required
def batch_delete_publish_content():
    """批量删除发布管理中的文案"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要删除的文案'})
        
        from app.models.image import ContentImage
        import os
        
        deleted_count = 0
        total_deleted_images = 0
        deleted_files = []
        
        for content_id in content_ids:
            content = Content.query.get(content_id)
            if not content or content.workflow_status not in ['ready_to_publish', 'client_approved', 'pending_publish']:
                continue
            
            # 删除关联的图片文件
            images = ContentImage.query.filter_by(content_id=content_id, is_deleted=False).all()
            
            for image in images:
                # 删除原图文件
                if image.image_path:
                    image_full_path = os.path.join(current_app.root_path, 'static', image.image_path.lstrip('/'))
                    if os.path.exists(image_full_path):
                        try:
                            os.remove(image_full_path)
                            deleted_files.append(image.image_path)
                        except Exception as e:
                            print(f"删除原图文件失败: {image_full_path}, 错误: {e}")
                
                # 删除缩略图文件
                if image.thumbnail_path:
                    thumbnail_full_path = os.path.join(current_app.root_path, 'static', image.thumbnail_path.lstrip('/'))
                    if os.path.exists(thumbnail_full_path):
                        try:
                            os.remove(thumbnail_full_path)
                            deleted_files.append(image.thumbnail_path)
                        except Exception as e:
                            print(f"删除缩略图文件失败: {thumbnail_full_path}, 错误: {e}")
                
                # 标记图片为已删除
                image.is_deleted = True
                image.deleted_at = datetime.now()
            
            # 标记文案为已删除
            content.is_deleted = True
            content.deleted_at = datetime.now()
            content.deleted_by = current_user.id
            
            deleted_count += 1
            total_deleted_images += len(images)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'批量删除成功！删除了 {deleted_count} 篇文案，{total_deleted_images} 张图片文件。',
            'deleted_count': deleted_count,
            'deleted_images_count': total_deleted_images
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量删除失败：{str(e)}'})

@main_simple_bp.route('/api/publish/start-publishing/<int:content_id>', methods=['POST'])
@login_required
def start_publishing(content_id):
    """模拟第三方API调用，将待发布状态改为发布中"""
    try:
        content = Content.query.get_or_404(content_id)
        
        # 检查文案状态是否为待发布
        if content.workflow_status != 'pending_publish':
            return jsonify({'success': False, 'message': '只能将待发布状态的文案改为发布中'})
        
        # 更新状态为发布中
        content.workflow_status = 'publishing'
        content.publish_status = 'publishing'
        content.status_update_time = datetime.now()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': '文案已开始发布'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'操作失败：{str(e)}'})

@main_simple_bp.route('/api/publish/complete/<int:content_id>', methods=['POST'])
@login_required
def complete_publishing(content_id):
    """模拟发布完成，将发布中状态改为发布成功"""
    try:
        data = request.get_json() or {}
        status = data.get('status', 'success')  # success 或 failed
        error_message = data.get('error_message', '')
        
        content = Content.query.get_or_404(content_id)
        
        # 检查文案状态是否为发布中
        if content.workflow_status != 'publishing':
            return jsonify({'success': False, 'message': '只能完成发布中状态的文案'})
        
        if status == 'success':
            # 发布成功
            content.workflow_status = 'published'
            content.publish_status = 'published'
            content.published_at = datetime.now()
            content.published_by = current_user.id
        else:
            # 发布失败
            content.workflow_status = 'publish_failed'
            content.publish_status = 'publish_failed'
            content.publish_error = error_message
        
        content.status_update_time = datetime.now()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': f'发布{status == "success" and "成功" or "失败"}'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'操作失败：{str(e)}'})

@main_simple_bp.route('/api/publish-status/batch-delete', methods=['POST'])
@login_required
def batch_delete_publish_status_content():
    """批量删除发布状态管理中的文案"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要删除的文案'})
        
        from app.models.image import ContentImage
        import os
        
        deleted_count = 0
        total_deleted_images = 0
        deleted_files = []
        
        for content_id in content_ids:
            content = Content.query.get(content_id)
            if not content or content.workflow_status not in ['pending_publish', 'publishing', 'published', 'publish_failed', 'publish_timeout']:
                continue
            
            # 删除关联的图片文件
            images = ContentImage.query.filter_by(content_id=content_id, is_deleted=False).all()
            
            for image in images:
                # 删除原图文件
                if image.image_path:
                    image_full_path = os.path.join(current_app.root_path, 'static', image.image_path.lstrip('/'))
                    if os.path.exists(image_full_path):
                        try:
                            os.remove(image_full_path)
                            deleted_files.append(image.image_path)
                        except Exception as e:
                            print(f"删除原图文件失败: {image_full_path}, 错误: {e}")
                
                # 删除缩略图文件
                if image.thumbnail_path:
                    thumbnail_full_path = os.path.join(current_app.root_path, 'static', image.thumbnail_path.lstrip('/'))
                    if os.path.exists(thumbnail_full_path):
                        try:
                            os.remove(thumbnail_full_path)
                            deleted_files.append(image.thumbnail_path)
                        except Exception as e:
                            print(f"删除缩略图文件失败: {thumbnail_full_path}, 错误: {e}")
                
                # 标记图片为已删除
                image.is_deleted = True
                image.deleted_at = datetime.now()
            
            # 标记文案为已删除
            content.is_deleted = True
            content.deleted_at = datetime.now()
            content.deleted_by = current_user.id
            
            deleted_count += 1
            total_deleted_images += len(images)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'批量删除成功！删除了 {deleted_count} 篇文案，{total_deleted_images} 张图片文件。',
            'deleted_count': deleted_count,
            'deleted_images_count': total_deleted_images
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量删除失败：{str(e)}'})

@main_simple_bp.route('/api/publish-status/batch-mark-status', methods=['POST'])
@login_required
def batch_mark_publish_status():
    """批量标记发布状态"""
    try:
        data = request.get_json()
        content_ids = data.get('content_ids', [])
        target_status = data.get('status')
        
        if not content_ids:
            return jsonify({'success': False, 'message': '请选择要操作的文案'})
        
        if not target_status:
            return jsonify({'success': False, 'message': '缺少目标状态参数'})
        
        # 状态映射
        status_mapping = {
            'pending_publish': {
                'workflow_status': 'pending_publish',
                'publish_status': 'pending_publish',
                'message': '待发布'
            },
            'published': {
                'workflow_status': 'published',
                'publish_status': 'published',
                'published_at': datetime.now(),
                'published_by': current_user.id,
                'message': '已发布'
            },
            'publish_failed': {
                'workflow_status': 'publish_failed',
                'publish_status': 'publish_failed',
                'publish_error': '批量标记为失败',
                'message': '发布失败'
            }
        }
        
        if target_status not in status_mapping:
            return jsonify({'success': False, 'message': '无效的状态值'})
        
        updated_count = 0
        for content_id in content_ids:
            content = Content.query.get(content_id)
            if not content or content.workflow_status not in ['pending_publish', 'publishing', 'published', 'publish_failed', 'publish_timeout']:
                continue
            
            # 更新状态
            status_config = status_mapping[target_status]
            content.workflow_status = status_config['workflow_status']
            content.publish_status = status_config['publish_status']
            content.status_update_time = datetime.now()
            
            # 特殊字段处理
            if 'published_at' in status_config:
                content.published_at = status_config['published_at']
            if 'published_by' in status_config:
                content.published_by = status_config['published_by']
            if 'publish_error' in status_config:
                content.publish_error = status_config['publish_error']
            
            updated_count += 1
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'批量标记成功！{updated_count} 篇文案已标记为{status_mapping[target_status]["message"]}。',
            'updated_count': updated_count
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'批量标记失败：{str(e)}'})
